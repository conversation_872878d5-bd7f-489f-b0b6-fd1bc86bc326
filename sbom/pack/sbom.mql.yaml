# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

packs:
  - uid: mondoo-sbom
    name: Mondoo SBOM
    filters:
      - mql: asset.family.contains("unix")
      - mql: asset.family.contains('windows')
    queries:
      - uid: mondoo-sbom-asset
        title: Retrieve information about the Platform
        mql: asset { name platform version build family arch ids labels cpes.map(uri) }
      - uid: mondoo-sbom-packages
        title: Retrieve list of installed packages
        mql: packages { name version purl cpes.map(uri) arch origin format files.map(path) }
      - uid: mondoo-sbom-python-packages
        title: Retrieve list of installed Python packages
        mql: python.packages { name version purl cpes.map(uri) file.path }
      - uid: mondoo-sbom-npm-packages
        title: Retrieve list of installed npm packages
        mql: npm.packages { name version purl cpes.map(uri) files.map(path) }
      - uid: mondoo-sbom-kernel-installed
        filters:
          - mql: |
              asset.family.contains('linux')
              asset.runtime != 'container' && asset.kind != 'container' && asset.kind != 'container-image'
        title: Retrieve information about the installed kernel
        mql: kernel.installed
