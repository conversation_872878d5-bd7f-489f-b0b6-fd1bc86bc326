// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

syntax = "proto3";

package mondoo.sbom.v1;

option go_package = "go.mondoo.com/cnquery/v11/sbom";

// Status represents the possible states of processing or generation for a
// Bill of Materials (BOM). Each status indicates a different stage or outcome
// in the lifecycle of a BOM.
enum Status {
  // UNSPECIFIED indicates an undefined status. This value is used as a default
  // when the status has not been set or is unknown. It suggests that the SBOM's
  // processing state is currently not available or has not been initialized.
  STATUS_UNSPECIFIED = 0;

  // SUCCEEDED indicates that the BOM was successfully processed or generated
  // without any issues. This status means that all components, packages, and
  // materials listed in the BOM have been accounted for and validated, and the
  // BOM is complete and accurate.
  STATUS_SUCCEEDED = 1;

  // PARTIALLY_SUCCEEDED indicates that the BOM was processed or generated with
  // some minor issues or omissions. While the majority of the SBOM is accurate,
  // there might be a few components or details that could not be  fully
  // validated or included. This status is typically used when the BOM is usable
  // but might require additional review or subsequent updates to ensure
  // completeness.
  STATUS_PARTIALLY_SUCCEEDED = 2;

  // FAILED indicates that the process of generating or processing the SBOM
  // encountered significant issues, resulting in an incomplete or inaccurate
  // document. This status is used when critical components or information
  // are missing, preventing the BOM from being usable or reliable. A failed
  // status typically requires a thorough review and reprocessing of the BOM.
  STATUS_FAILED = 3;

  // STARTED indicates that the SBOM scan was initialized. It is used 
  // for tracking progress of a scan over time.
  STATUS_STARTED = 4;
}

// Sbom (Software Bill of Materials) represents a comprehensive inventory of
// software packages. It is a structured list of all software components that
// are part of a given asset, such as a virtual machine or container. The Sbom
// is used to identify known vulnerabilities and other security issues within
// the scanned asset.
message Sbom {
  // Details the tool used to generate the BOM. This includes information about
  // the software or service that created the BOM, such as its name, version,
  // and other relevant metadata.
  Generator generator = 1;

  // Indicates when the BOM was generated or last updated. It is expected to
  // follow the RFC 3339 format. This timestamp is essential for maintaining the
  // relevance of the BOM, as software components can frequently change.
  string timestamp = 2;

  // 'status' is an optional field that denotes the current state or processing
  // status of the BOM. It can indicate whether the BOM generation was
  // successful, partially successful, or failed. This status helps in
  // determining the reliability and completeness of the BOM.
  Status status = 3;

  // Represents the primary asset for which the BOM is created. This could be a
  // virtual machine, a container, an application, or any other logical unit in
  // the IT environment. The asset details the context or scope for which the
  // software components in the BOM are relevant.
  Asset asset = 4;

  // 'packages' is a list of all software packages or components included in the
  // BOM. Each package entry contains detailed information such as the package
  // name, version, and other identifiers. This list forms the core of the BOM,
  // providing a detailed account of the software composition of the asset.
  repeated Package packages = 5;

  // 'error_message' is and optional field that describes the error from a 
  // failed scan
  string error_message = 6;
}

// Source describes the provider of the BOM data, which in this case is the
// always Mondoo.
message Generator {
  // The vendor name or the organization that provided the BOM.
  string vendor = 1;
  // The name of the product or service this BOM pertains to.
  string name = 2;
  // The version of the product or service.
  string version = 3;
  // The URL of the product or service.
  string url = 4;
}

// ExternalIDType enumerates the different types of external identifiers that
// can be used to reference external resources or entities. Each type
// corresponds to a specific kind of identifier, such as an AWS account number
// or an Amazon Resource Name (ARN).
enum ExternalIDType {
  // EXTERNAL_ID_UNSPECIFIED indicates an undefined or unknown type of external
  // ID. This default value is used when the external ID type has not been set
  // or is not applicable.
  EXTERNAL_ID_TYPE_UNSPECIFIED = 0;

  // EXTERNAL_ID_AWS_ACCOUNT represents an AWS account identifier. This type is
  // used when the external ID refers specifically to an AWS account number,
  // which is a unique identifier for an AWS account.
  EXTERNAL_ID_TYPE_AWS_ACCOUNT = 1;

  // EXTERNAL_ID_AWS_ARN denotes an Amazon Resource Name (ARN), a standardized
  // format used by AWS to uniquely identify resources within the AWS ecosystem.
  // ARNs are used in various AWS services to refer to specific resources like
  // IAM roles, S3 buckets, or EC2 instances.
  EXTERNAL_ID_TYPE_AWS_ARN = 2;

  // EXTERNAL_ID_TYPE_AWS_ORG denotes the organization to which the AWS Account
  // and all subresources belong.
  EXTERNAL_ID_TYPE_AWS_ORG = 3;

  // EXTERNAL_ID_TYPE_AZURE_SUB represents an Azure subscription identifier. 
  // This type is used when the external ID refers specifically to an Azure
  // subscription number, which is a unique identifier for an Azure 
  // subscription.
  EXTERNAL_ID_TYPE_AZURE_SUB = 4;

  // EXTERNAL_ID_TYPE_AZURE_ID represents a fully qualified Azure resource 
  // identifier, a standarized format used by Azure to uniquely identify 
  // resources within the Azure ecosystem.
  EXTERNAL_ID_TYPE_AZURE_ID = 5;
}

// ExternalID encapsulates an external identifier and its type. This structure
// is useful for referencing resources or entities that are external to the
// system or application using this data model.
message ExternalID {
  // 'type' specifies the kind of external identifier being used, such as an AWS
  // account number or an ARN. The type provides context to the 'id' field,
  // indicating how the ID should be interpreted and used.
  ExternalIDType type = 1;

  // 'id' is the actual identifier of the external resource. The format and
  // interpretation of this ID depend on the 'type'. For example, it could be a
  // numeric string for AWS account numbers or a structured string for ARNs.
  // This ID is used to uniquely identify or reference an external entity or
  // resource.
  string id = 2;
}

// Asset represents a digital or physical resource in an IT environment. It
// could be software, hardware, or any identifiable entity within the
// infrastructure.
message Asset {
  // 'name' is a human-readable identifier for the asset. This field should
  // contain a descriptive name that easily identifies the asset within the
  // system.
  string name = 3;

  // 'platform_ids' are unique identifiers that represent the asset within
  // various platforms. These IDs follow the MRN format and can point to
  // specific resources within a given platform. Example formats include
  // identifiers for AWS accounts or EC2 instances. Sample platform ids are:
  // - //platformid.api.mondoo.app/runtime/aws/accounts/************
  // - //platformid.api.mondoo.app/runtime/aws/ec2/v1/accounts/************
  repeated string platform_ids = 4;

  // 'external_ids' are a collection of identifiers that link the asset to
  // external systems or resources. Each external ID is associated with a type
  // that specifies the kind of identifier (e.g., AWS account number, ARN).
  repeated ExternalID external_ids = 5;

  // 'platform' provides detailed information about the platform of the asset,
  // such as operating system, architecture, and version.
  Platform platform = 6;

  // 'labels' are key-value pairs used for categorizing or annotating the asset.
  // These labels are sourced from the system that is the asset owner. For
  // example, labels for EC2 instance are populated from the EC2 metadata tags.
  map<string, string> labels = 18;

  // 'trace_id' is used to link the asset to a specific identifier
  // that is passed in by the user. if the user provides no value, 
  // this will be empty.
  string trace_id = 19;
}

// Platform describes the technical platform or environment of an asset. This
// could be an operating system, a hardware architecture, a cloud resource or
// another defining characteristic of the asset's environment.
message Platform {
  // The technical name of the platform, e.g., "amazonlinux".
  string name = 1;
  // The architecture type, e.g., "x86_64".
  string arch = 3;
  // A human-readable title of the platform, e.g., "Amazon Linux 2".
  string title = 4;
  // Categories or families the platform belongs to, e.g., "linux", "unix".
  // protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
  repeated string family = 5;
  // An optional field indicating the build number of the platform.
  string build = 6;
  // The version of the platform, e.g., "2".
  string version = 7;
  // 'labels' are additional key-value pairs that provide more context or
  // metadata about the platform.
  map<string, string> labels = 22;
  // The Common Platform Enumeration (CPE) for the platform.
  repeated string cpes = 23;
}

// Package details an individual software package or library.
message Package {
  // The name of the package.
  string name = 1;
  // The version of the package.
  string version = 2;
  // The architecture of the package
  string architecture = 3;
  // The Common Platform Enumeration (CPE) name
  repeated string cpes = 4;
  // The Package URL (pURL), a standardized format for defining and locating
  // software package metadata.
  string purl = 5;
  // location on disk
  // Deprecated: use evidence instead
  string location = 6;
  // 'type' indicates the type of package, such as a rpm, dpkg, or gem.
  string type = 7;
  // description of the package
  string description = 20;
  // 'evidence_list' is a collection of evidence that supports the presence of
  // the package in the asset. This evidence could include eg. file paths
  repeated Evidence evidence_list = 21;
  // Package Origin (e.g. other package name, or source of the package)
  string origin = 22;
  // Package Vendor/Publisher
  string vendor = 23;
  // Status of the package
  string status = 24;
}

enum EvidenceType {
  EVIDENCE_TYPE_UNSPECIFIED = 0;
  EVIDENCE_TYPE_FILE = 1;
}

message Evidence {
  // 'type' indicates the type of evidence, such as a file path.
  EvidenceType type = 1;
  // 'value' is the actual evidence that supports the presence of the package in
  // the asset. The format and interpretation of this value depend on the
  // 'type'. For example, it could be a file path for file evidence.
  string value = 2;
}