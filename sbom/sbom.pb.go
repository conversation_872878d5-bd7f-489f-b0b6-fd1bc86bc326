// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: sbom.proto

package sbom

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status represents the possible states of processing or generation for a
// Bill of Materials (BOM). Each status indicates a different stage or outcome
// in the lifecycle of a BOM.
type Status int32

const (
	// UNSPECIFIED indicates an undefined status. This value is used as a default
	// when the status has not been set or is unknown. It suggests that the SBOM's
	// processing state is currently not available or has not been initialized.
	Status_STATUS_UNSPECIFIED Status = 0
	// SUCCEEDED indicates that the BOM was successfully processed or generated
	// without any issues. This status means that all components, packages, and
	// materials listed in the BOM have been accounted for and validated, and the
	// BOM is complete and accurate.
	Status_STATUS_SUCCEEDED Status = 1
	// PARTIALLY_SUCCEEDED indicates that the BOM was processed or generated with
	// some minor issues or omissions. While the majority of the SBOM is accurate,
	// there might be a few components or details that could not be  fully
	// validated or included. This status is typically used when the BOM is usable
	// but might require additional review or subsequent updates to ensure
	// completeness.
	Status_STATUS_PARTIALLY_SUCCEEDED Status = 2
	// FAILED indicates that the process of generating or processing the SBOM
	// encountered significant issues, resulting in an incomplete or inaccurate
	// document. This status is used when critical components or information
	// are missing, preventing the BOM from being usable or reliable. A failed
	// status typically requires a thorough review and reprocessing of the BOM.
	Status_STATUS_FAILED Status = 3
	// STARTED indicates that the SBOM scan was initialized. It is used
	// for tracking progress of a scan over time.
	Status_STATUS_STARTED Status = 4
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_SUCCEEDED",
		2: "STATUS_PARTIALLY_SUCCEEDED",
		3: "STATUS_FAILED",
		4: "STATUS_STARTED",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":         0,
		"STATUS_SUCCEEDED":           1,
		"STATUS_PARTIALLY_SUCCEEDED": 2,
		"STATUS_FAILED":              3,
		"STATUS_STARTED":             4,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_sbom_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_sbom_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{0}
}

// ExternalIDType enumerates the different types of external identifiers that
// can be used to reference external resources or entities. Each type
// corresponds to a specific kind of identifier, such as an AWS account number
// or an Amazon Resource Name (ARN).
type ExternalIDType int32

const (
	// EXTERNAL_ID_UNSPECIFIED indicates an undefined or unknown type of external
	// ID. This default value is used when the external ID type has not been set
	// or is not applicable.
	ExternalIDType_EXTERNAL_ID_TYPE_UNSPECIFIED ExternalIDType = 0
	// EXTERNAL_ID_AWS_ACCOUNT represents an AWS account identifier. This type is
	// used when the external ID refers specifically to an AWS account number,
	// which is a unique identifier for an AWS account.
	ExternalIDType_EXTERNAL_ID_TYPE_AWS_ACCOUNT ExternalIDType = 1
	// EXTERNAL_ID_AWS_ARN denotes an Amazon Resource Name (ARN), a standardized
	// format used by AWS to uniquely identify resources within the AWS ecosystem.
	// ARNs are used in various AWS services to refer to specific resources like
	// IAM roles, S3 buckets, or EC2 instances.
	ExternalIDType_EXTERNAL_ID_TYPE_AWS_ARN ExternalIDType = 2
	// EXTERNAL_ID_TYPE_AWS_ORG denotes the organization to which the AWS Account
	// and all subresources belong.
	ExternalIDType_EXTERNAL_ID_TYPE_AWS_ORG ExternalIDType = 3
	// EXTERNAL_ID_TYPE_AZURE_SUB represents an Azure subscription identifier.
	// This type is used when the external ID refers specifically to an Azure
	// subscription number, which is a unique identifier for an Azure
	// subscription.
	ExternalIDType_EXTERNAL_ID_TYPE_AZURE_SUB ExternalIDType = 4
	// EXTERNAL_ID_TYPE_AZURE_ID represents a fully qualified Azure resource
	// identifier, a standarized format used by Azure to uniquely identify
	// resources within the Azure ecosystem.
	ExternalIDType_EXTERNAL_ID_TYPE_AZURE_ID ExternalIDType = 5
)

// Enum value maps for ExternalIDType.
var (
	ExternalIDType_name = map[int32]string{
		0: "EXTERNAL_ID_TYPE_UNSPECIFIED",
		1: "EXTERNAL_ID_TYPE_AWS_ACCOUNT",
		2: "EXTERNAL_ID_TYPE_AWS_ARN",
		3: "EXTERNAL_ID_TYPE_AWS_ORG",
		4: "EXTERNAL_ID_TYPE_AZURE_SUB",
		5: "EXTERNAL_ID_TYPE_AZURE_ID",
	}
	ExternalIDType_value = map[string]int32{
		"EXTERNAL_ID_TYPE_UNSPECIFIED": 0,
		"EXTERNAL_ID_TYPE_AWS_ACCOUNT": 1,
		"EXTERNAL_ID_TYPE_AWS_ARN":     2,
		"EXTERNAL_ID_TYPE_AWS_ORG":     3,
		"EXTERNAL_ID_TYPE_AZURE_SUB":   4,
		"EXTERNAL_ID_TYPE_AZURE_ID":    5,
	}
)

func (x ExternalIDType) Enum() *ExternalIDType {
	p := new(ExternalIDType)
	*p = x
	return p
}

func (x ExternalIDType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExternalIDType) Descriptor() protoreflect.EnumDescriptor {
	return file_sbom_proto_enumTypes[1].Descriptor()
}

func (ExternalIDType) Type() protoreflect.EnumType {
	return &file_sbom_proto_enumTypes[1]
}

func (x ExternalIDType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExternalIDType.Descriptor instead.
func (ExternalIDType) EnumDescriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{1}
}

type EvidenceType int32

const (
	EvidenceType_EVIDENCE_TYPE_UNSPECIFIED EvidenceType = 0
	EvidenceType_EVIDENCE_TYPE_FILE        EvidenceType = 1
)

// Enum value maps for EvidenceType.
var (
	EvidenceType_name = map[int32]string{
		0: "EVIDENCE_TYPE_UNSPECIFIED",
		1: "EVIDENCE_TYPE_FILE",
	}
	EvidenceType_value = map[string]int32{
		"EVIDENCE_TYPE_UNSPECIFIED": 0,
		"EVIDENCE_TYPE_FILE":        1,
	}
)

func (x EvidenceType) Enum() *EvidenceType {
	p := new(EvidenceType)
	*p = x
	return p
}

func (x EvidenceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceType) Descriptor() protoreflect.EnumDescriptor {
	return file_sbom_proto_enumTypes[2].Descriptor()
}

func (EvidenceType) Type() protoreflect.EnumType {
	return &file_sbom_proto_enumTypes[2]
}

func (x EvidenceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceType.Descriptor instead.
func (EvidenceType) EnumDescriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{2}
}

// Sbom (Software Bill of Materials) represents a comprehensive inventory of
// software packages. It is a structured list of all software components that
// are part of a given asset, such as a virtual machine or container. The Sbom
// is used to identify known vulnerabilities and other security issues within
// the scanned asset.
type Sbom struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Details the tool used to generate the BOM. This includes information about
	// the software or service that created the BOM, such as its name, version,
	// and other relevant metadata.
	Generator *Generator `protobuf:"bytes,1,opt,name=generator,proto3" json:"generator,omitempty"`
	// Indicates when the BOM was generated or last updated. It is expected to
	// follow the RFC 3339 format. This timestamp is essential for maintaining the
	// relevance of the BOM, as software components can frequently change.
	Timestamp string `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 'status' is an optional field that denotes the current state or processing
	// status of the BOM. It can indicate whether the BOM generation was
	// successful, partially successful, or failed. This status helps in
	// determining the reliability and completeness of the BOM.
	Status Status `protobuf:"varint,3,opt,name=status,proto3,enum=mondoo.sbom.v1.Status" json:"status,omitempty"`
	// Represents the primary asset for which the BOM is created. This could be a
	// virtual machine, a container, an application, or any other logical unit in
	// the IT environment. The asset details the context or scope for which the
	// software components in the BOM are relevant.
	Asset *Asset `protobuf:"bytes,4,opt,name=asset,proto3" json:"asset,omitempty"`
	// 'packages' is a list of all software packages or components included in the
	// BOM. Each package entry contains detailed information such as the package
	// name, version, and other identifiers. This list forms the core of the BOM,
	// providing a detailed account of the software composition of the asset.
	Packages []*Package `protobuf:"bytes,5,rep,name=packages,proto3" json:"packages,omitempty"`
	// 'error_message' is and optional field that describes the error from a
	// failed scan
	ErrorMessage  string `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sbom) Reset() {
	*x = Sbom{}
	mi := &file_sbom_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sbom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sbom) ProtoMessage() {}

func (x *Sbom) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sbom.ProtoReflect.Descriptor instead.
func (*Sbom) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{0}
}

func (x *Sbom) GetGenerator() *Generator {
	if x != nil {
		return x.Generator
	}
	return nil
}

func (x *Sbom) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *Sbom) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *Sbom) GetAsset() *Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *Sbom) GetPackages() []*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *Sbom) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// Source describes the provider of the BOM data, which in this case is the
// always Mondoo.
type Generator struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The vendor name or the organization that provided the BOM.
	Vendor string `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// The name of the product or service this BOM pertains to.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The version of the product or service.
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// The URL of the product or service.
	Url           string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Generator) Reset() {
	*x = Generator{}
	mi := &file_sbom_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Generator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Generator) ProtoMessage() {}

func (x *Generator) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Generator.ProtoReflect.Descriptor instead.
func (*Generator) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{1}
}

func (x *Generator) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *Generator) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Generator) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Generator) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// ExternalID encapsulates an external identifier and its type. This structure
// is useful for referencing resources or entities that are external to the
// system or application using this data model.
type ExternalID struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 'type' specifies the kind of external identifier being used, such as an AWS
	// account number or an ARN. The type provides context to the 'id' field,
	// indicating how the ID should be interpreted and used.
	Type ExternalIDType `protobuf:"varint,1,opt,name=type,proto3,enum=mondoo.sbom.v1.ExternalIDType" json:"type,omitempty"`
	// 'id' is the actual identifier of the external resource. The format and
	// interpretation of this ID depend on the 'type'. For example, it could be a
	// numeric string for AWS account numbers or a structured string for ARNs.
	// This ID is used to uniquely identify or reference an external entity or
	// resource.
	Id            string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalID) Reset() {
	*x = ExternalID{}
	mi := &file_sbom_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalID) ProtoMessage() {}

func (x *ExternalID) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalID.ProtoReflect.Descriptor instead.
func (*ExternalID) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{2}
}

func (x *ExternalID) GetType() ExternalIDType {
	if x != nil {
		return x.Type
	}
	return ExternalIDType_EXTERNAL_ID_TYPE_UNSPECIFIED
}

func (x *ExternalID) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Asset represents a digital or physical resource in an IT environment. It
// could be software, hardware, or any identifiable entity within the
// infrastructure.
type Asset struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 'name' is a human-readable identifier for the asset. This field should
	// contain a descriptive name that easily identifies the asset within the
	// system.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 'platform_ids' are unique identifiers that represent the asset within
	// various platforms. These IDs follow the MRN format and can point to
	// specific resources within a given platform. Example formats include
	// identifiers for AWS accounts or EC2 instances. Sample platform ids are:
	// - //platformid.api.mondoo.app/runtime/aws/accounts/************
	// - //platformid.api.mondoo.app/runtime/aws/ec2/v1/accounts/************
	PlatformIds []string `protobuf:"bytes,4,rep,name=platform_ids,json=platformIds,proto3" json:"platform_ids,omitempty"`
	// 'external_ids' are a collection of identifiers that link the asset to
	// external systems or resources. Each external ID is associated with a type
	// that specifies the kind of identifier (e.g., AWS account number, ARN).
	ExternalIds []*ExternalID `protobuf:"bytes,5,rep,name=external_ids,json=externalIds,proto3" json:"external_ids,omitempty"`
	// 'platform' provides detailed information about the platform of the asset,
	// such as operating system, architecture, and version.
	Platform *Platform `protobuf:"bytes,6,opt,name=platform,proto3" json:"platform,omitempty"`
	// 'labels' are key-value pairs used for categorizing or annotating the asset.
	// These labels are sourced from the system that is the asset owner. For
	// example, labels for EC2 instance are populated from the EC2 metadata tags.
	Labels map[string]string `protobuf:"bytes,18,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// 'trace_id' is used to link the asset to a specific identifier
	// that is passed in by the user. if the user provides no value,
	// this will be empty.
	TraceId       string `protobuf:"bytes,19,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_sbom_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{3}
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetPlatformIds() []string {
	if x != nil {
		return x.PlatformIds
	}
	return nil
}

func (x *Asset) GetExternalIds() []*ExternalID {
	if x != nil {
		return x.ExternalIds
	}
	return nil
}

func (x *Asset) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *Asset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Asset) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// Platform describes the technical platform or environment of an asset. This
// could be an operating system, a hardware architecture, a cloud resource or
// another defining characteristic of the asset's environment.
type Platform struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The technical name of the platform, e.g., "amazonlinux".
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The architecture type, e.g., "x86_64".
	Arch string `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	// A human-readable title of the platform, e.g., "Amazon Linux 2".
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// Categories or families the platform belongs to, e.g., "linux", "unix".
	// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
	Family []string `protobuf:"bytes,5,rep,name=family,proto3" json:"family,omitempty"`
	// An optional field indicating the build number of the platform.
	Build string `protobuf:"bytes,6,opt,name=build,proto3" json:"build,omitempty"`
	// The version of the platform, e.g., "2".
	Version string `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	// 'labels' are additional key-value pairs that provide more context or
	// metadata about the platform.
	Labels map[string]string `protobuf:"bytes,22,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// The Common Platform Enumeration (CPE) for the platform.
	Cpes          []string `protobuf:"bytes,23,rep,name=cpes,proto3" json:"cpes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Platform) Reset() {
	*x = Platform{}
	mi := &file_sbom_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Platform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Platform) ProtoMessage() {}

func (x *Platform) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Platform.ProtoReflect.Descriptor instead.
func (*Platform) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{4}
}

func (x *Platform) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Platform) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *Platform) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Platform) GetFamily() []string {
	if x != nil {
		return x.Family
	}
	return nil
}

func (x *Platform) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

func (x *Platform) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Platform) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Platform) GetCpes() []string {
	if x != nil {
		return x.Cpes
	}
	return nil
}

// Package details an individual software package or library.
type Package struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the package.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The version of the package.
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// The architecture of the package
	Architecture string `protobuf:"bytes,3,opt,name=architecture,proto3" json:"architecture,omitempty"`
	// The Common Platform Enumeration (CPE) name
	Cpes []string `protobuf:"bytes,4,rep,name=cpes,proto3" json:"cpes,omitempty"`
	// The Package URL (pURL), a standardized format for defining and locating
	// software package metadata.
	Purl string `protobuf:"bytes,5,opt,name=purl,proto3" json:"purl,omitempty"`
	// location on disk
	// Deprecated: use evidence instead
	Location string `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"`
	// 'type' indicates the type of package, such as a rpm, dpkg, or gem.
	Type string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	// description of the package
	Description string `protobuf:"bytes,20,opt,name=description,proto3" json:"description,omitempty"`
	// 'evidence_list' is a collection of evidence that supports the presence of
	// the package in the asset. This evidence could include eg. file paths
	EvidenceList []*Evidence `protobuf:"bytes,21,rep,name=evidence_list,json=evidenceList,proto3" json:"evidence_list,omitempty"`
	// Package Origin (e.g. other package name, or source of the package)
	Origin string `protobuf:"bytes,22,opt,name=origin,proto3" json:"origin,omitempty"`
	// Package Vendor/Publisher
	Vendor string `protobuf:"bytes,23,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// Status of the package
	Status        string `protobuf:"bytes,24,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Package) Reset() {
	*x = Package{}
	mi := &file_sbom_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{5}
}

func (x *Package) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Package) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Package) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *Package) GetCpes() []string {
	if x != nil {
		return x.Cpes
	}
	return nil
}

func (x *Package) GetPurl() string {
	if x != nil {
		return x.Purl
	}
	return ""
}

func (x *Package) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Package) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Package) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Package) GetEvidenceList() []*Evidence {
	if x != nil {
		return x.EvidenceList
	}
	return nil
}

func (x *Package) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *Package) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *Package) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type Evidence struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 'type' indicates the type of evidence, such as a file path.
	Type EvidenceType `protobuf:"varint,1,opt,name=type,proto3,enum=mondoo.sbom.v1.EvidenceType" json:"type,omitempty"`
	// 'value' is the actual evidence that supports the presence of the package in
	// the asset. The format and interpretation of this value depend on the
	// 'type'. For example, it could be a file path for file evidence.
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Evidence) Reset() {
	*x = Evidence{}
	mi := &file_sbom_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Evidence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Evidence) ProtoMessage() {}

func (x *Evidence) ProtoReflect() protoreflect.Message {
	mi := &file_sbom_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Evidence.ProtoReflect.Descriptor instead.
func (*Evidence) Descriptor() ([]byte, []int) {
	return file_sbom_proto_rawDescGZIP(), []int{6}
}

func (x *Evidence) GetType() EvidenceType {
	if x != nil {
		return x.Type
	}
	return EvidenceType_EVIDENCE_TYPE_UNSPECIFIED
}

func (x *Evidence) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_sbom_proto protoreflect.FileDescriptor

const file_sbom_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"sbom.proto\x12\x0emondoo.sbom.v1\"\x94\x02\n" +
	"\x04Sbom\x127\n" +
	"\tgenerator\x18\x01 \x01(\v2\x19.mondoo.sbom.v1.GeneratorR\tgenerator\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\tR\ttimestamp\x12.\n" +
	"\x06status\x18\x03 \x01(\x0e2\x16.mondoo.sbom.v1.StatusR\x06status\x12+\n" +
	"\x05asset\x18\x04 \x01(\v2\x15.mondoo.sbom.v1.AssetR\x05asset\x123\n" +
	"\bpackages\x18\x05 \x03(\v2\x17.mondoo.sbom.v1.PackageR\bpackages\x12#\n" +
	"\rerror_message\x18\x06 \x01(\tR\ferrorMessage\"c\n" +
	"\tGenerator\x12\x16\n" +
	"\x06vendor\x18\x01 \x01(\tR\x06vendor\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12\x10\n" +
	"\x03url\x18\x04 \x01(\tR\x03url\"P\n" +
	"\n" +
	"ExternalID\x122\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1e.mondoo.sbom.v1.ExternalIDTypeR\x04type\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\"\xc4\x02\n" +
	"\x05Asset\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12!\n" +
	"\fplatform_ids\x18\x04 \x03(\tR\vplatformIds\x12=\n" +
	"\fexternal_ids\x18\x05 \x03(\v2\x1a.mondoo.sbom.v1.ExternalIDR\vexternalIds\x124\n" +
	"\bplatform\x18\x06 \x01(\v2\x18.mondoo.sbom.v1.PlatformR\bplatform\x129\n" +
	"\x06labels\x18\x12 \x03(\v2!.mondoo.sbom.v1.Asset.LabelsEntryR\x06labels\x12\x19\n" +
	"\btrace_id\x18\x13 \x01(\tR\atraceId\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9d\x02\n" +
	"\bPlatform\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04arch\x18\x03 \x01(\tR\x04arch\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x16\n" +
	"\x06family\x18\x05 \x03(\tR\x06family\x12\x14\n" +
	"\x05build\x18\x06 \x01(\tR\x05build\x12\x18\n" +
	"\aversion\x18\a \x01(\tR\aversion\x12<\n" +
	"\x06labels\x18\x16 \x03(\v2$.mondoo.sbom.v1.Platform.LabelsEntryR\x06labels\x12\x12\n" +
	"\x04cpes\x18\x17 \x03(\tR\x04cpes\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xdc\x02\n" +
	"\aPackage\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\"\n" +
	"\farchitecture\x18\x03 \x01(\tR\farchitecture\x12\x12\n" +
	"\x04cpes\x18\x04 \x03(\tR\x04cpes\x12\x12\n" +
	"\x04purl\x18\x05 \x01(\tR\x04purl\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12 \n" +
	"\vdescription\x18\x14 \x01(\tR\vdescription\x12=\n" +
	"\revidence_list\x18\x15 \x03(\v2\x18.mondoo.sbom.v1.EvidenceR\fevidenceList\x12\x16\n" +
	"\x06origin\x18\x16 \x01(\tR\x06origin\x12\x16\n" +
	"\x06vendor\x18\x17 \x01(\tR\x06vendor\x12\x16\n" +
	"\x06status\x18\x18 \x01(\tR\x06status\"R\n" +
	"\bEvidence\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.mondoo.sbom.v1.EvidenceTypeR\x04type\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value*}\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\x14\n" +
	"\x10STATUS_SUCCEEDED\x10\x01\x12\x1e\n" +
	"\x1aSTATUS_PARTIALLY_SUCCEEDED\x10\x02\x12\x11\n" +
	"\rSTATUS_FAILED\x10\x03\x12\x12\n" +
	"\x0eSTATUS_STARTED\x10\x04*\xcf\x01\n" +
	"\x0eExternalIDType\x12 \n" +
	"\x1cEXTERNAL_ID_TYPE_UNSPECIFIED\x10\x00\x12 \n" +
	"\x1cEXTERNAL_ID_TYPE_AWS_ACCOUNT\x10\x01\x12\x1c\n" +
	"\x18EXTERNAL_ID_TYPE_AWS_ARN\x10\x02\x12\x1c\n" +
	"\x18EXTERNAL_ID_TYPE_AWS_ORG\x10\x03\x12\x1e\n" +
	"\x1aEXTERNAL_ID_TYPE_AZURE_SUB\x10\x04\x12\x1d\n" +
	"\x19EXTERNAL_ID_TYPE_AZURE_ID\x10\x05*E\n" +
	"\fEvidenceType\x12\x1d\n" +
	"\x19EVIDENCE_TYPE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12EVIDENCE_TYPE_FILE\x10\x01B Z\x1ego.mondoo.com/cnquery/v11/sbomb\x06proto3"

var (
	file_sbom_proto_rawDescOnce sync.Once
	file_sbom_proto_rawDescData []byte
)

func file_sbom_proto_rawDescGZIP() []byte {
	file_sbom_proto_rawDescOnce.Do(func() {
		file_sbom_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_sbom_proto_rawDesc), len(file_sbom_proto_rawDesc)))
	})
	return file_sbom_proto_rawDescData
}

var file_sbom_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_sbom_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_sbom_proto_goTypes = []any{
	(Status)(0),         // 0: mondoo.sbom.v1.Status
	(ExternalIDType)(0), // 1: mondoo.sbom.v1.ExternalIDType
	(EvidenceType)(0),   // 2: mondoo.sbom.v1.EvidenceType
	(*Sbom)(nil),        // 3: mondoo.sbom.v1.Sbom
	(*Generator)(nil),   // 4: mondoo.sbom.v1.Generator
	(*ExternalID)(nil),  // 5: mondoo.sbom.v1.ExternalID
	(*Asset)(nil),       // 6: mondoo.sbom.v1.Asset
	(*Platform)(nil),    // 7: mondoo.sbom.v1.Platform
	(*Package)(nil),     // 8: mondoo.sbom.v1.Package
	(*Evidence)(nil),    // 9: mondoo.sbom.v1.Evidence
	nil,                 // 10: mondoo.sbom.v1.Asset.LabelsEntry
	nil,                 // 11: mondoo.sbom.v1.Platform.LabelsEntry
}
var file_sbom_proto_depIdxs = []int32{
	4,  // 0: mondoo.sbom.v1.Sbom.generator:type_name -> mondoo.sbom.v1.Generator
	0,  // 1: mondoo.sbom.v1.Sbom.status:type_name -> mondoo.sbom.v1.Status
	6,  // 2: mondoo.sbom.v1.Sbom.asset:type_name -> mondoo.sbom.v1.Asset
	8,  // 3: mondoo.sbom.v1.Sbom.packages:type_name -> mondoo.sbom.v1.Package
	1,  // 4: mondoo.sbom.v1.ExternalID.type:type_name -> mondoo.sbom.v1.ExternalIDType
	5,  // 5: mondoo.sbom.v1.Asset.external_ids:type_name -> mondoo.sbom.v1.ExternalID
	7,  // 6: mondoo.sbom.v1.Asset.platform:type_name -> mondoo.sbom.v1.Platform
	10, // 7: mondoo.sbom.v1.Asset.labels:type_name -> mondoo.sbom.v1.Asset.LabelsEntry
	11, // 8: mondoo.sbom.v1.Platform.labels:type_name -> mondoo.sbom.v1.Platform.LabelsEntry
	9,  // 9: mondoo.sbom.v1.Package.evidence_list:type_name -> mondoo.sbom.v1.Evidence
	2,  // 10: mondoo.sbom.v1.Evidence.type:type_name -> mondoo.sbom.v1.EvidenceType
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_sbom_proto_init() }
func file_sbom_proto_init() {
	if File_sbom_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_sbom_proto_rawDesc), len(file_sbom_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_sbom_proto_goTypes,
		DependencyIndexes: file_sbom_proto_depIdxs,
		EnumInfos:         file_sbom_proto_enumTypes,
		MessageInfos:      file_sbom_proto_msgTypes,
	}.Build()
	File_sbom_proto = out.File
	file_sbom_proto_goTypes = nil
	file_sbom_proto_depIdxs = nil
}
