$ cnquery sbom --help
Generate a software bill of materials (SBOM) for a given asset. The SBOM
is a representation of the asset's software components and their dependencies.

The following formats are supported:
- list (default)
- cnquery-json
- cyclonedx-json
- cyclonedx-xml
- spdx-json
- spdx-tag-value

Note this command is experimental and may change in the future.

Usage:
  cnquery sbom [flags]
  cnquery sbom [command]

Available Commands:
  container   Collect a software bill of materials (SBOM) for a running container or container image
  docker      Collect a software bill of materials (SBOM) for a running Docker container, Docker image, or Dockerfile
  filesystem  Collect a software bill of materials (SBOM) for a mounted file system target
  local       Collect a software bill of materials (SBOM) for your local system
  sbom        Collect a software bill of materials (SBOM) for read SBOM file on disk
  ssh         Collect a software bill of materials (SBOM) for a remote system via SSH
  vagrant     Collect a software bill of materials (SBOM) for a Vagrant host
  winrm       Collect a software bill of materials (SBOM) for a remote system via WinRM

Flags:
      --annotation stringToString   Add an annotation to the asset (default [])
      --asset-name string           User-override for the asset name
      --discover strings            Enable the discovery of nested assets. Supports: all, auto, container, container-images
  -h, --help                        help for sbom
  -o, --output string               Set output format: json, cyclonedx-json, cyclonedx-xml, spdx-json, spdx-tag-value, table (default "list")
      --output-target string        Set output target to which the SBOM report will be written
      --record string               Record all resource calls and use resources in the recording
      --sudo                        Elevate privileges with sudo
      --use-recording string        Use a recording to inject resource data (read-only)
      --with-evidence               Display evidence for each component

Global Flags:
      --api-proxy string   Set proxy for communications with Mondoo Platform API
      --auto-update        Enable automatic provider installation and update (default true)
      --config string      Set config file path (default $HOME/.config/mondoo/mondoo.yml)
      --log-level string   Set log level: error, warn, info, debug, trace (default "info")
  -v, --verbose            Enable verbose output

Use "cnquery sbom [command] --help" for more information about a command.
