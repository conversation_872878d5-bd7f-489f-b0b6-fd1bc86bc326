// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cnquery_report.proto

package reporter

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Report struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Assets        map[string]*Asset      `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Data          map[string]*DataValues `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Errors        map[string]string      `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Report) Reset() {
	*x = Report{}
	mi := &file_cnquery_report_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_report_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_cnquery_report_proto_rawDescGZIP(), []int{0}
}

func (x *Report) GetAssets() map[string]*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *Report) GetData() map[string]*DataValues {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Report) GetErrors() map[string]string {
	if x != nil {
		return x.Errors
	}
	return nil
}

type Asset struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	PlatformName  string                 `protobuf:"bytes,20,opt,name=platform_name,json=platformName,proto3" json:"platform_name,omitempty"`
	TraceId       string                 `protobuf:"bytes,21,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_cnquery_report_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_report_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_cnquery_report_proto_rawDescGZIP(), []int{1}
}

func (x *Asset) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Asset) GetPlatformName() string {
	if x != nil {
		return x.PlatformName
	}
	return ""
}

func (x *Asset) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

type DataValues struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Values        map[string]*DataValue  `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataValues) Reset() {
	*x = DataValues{}
	mi := &file_cnquery_report_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValues) ProtoMessage() {}

func (x *DataValues) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_report_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValues.ProtoReflect.Descriptor instead.
func (*DataValues) Descriptor() ([]byte, []int) {
	return file_cnquery_report_proto_rawDescGZIP(), []int{2}
}

func (x *DataValues) GetValues() map[string]*DataValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type DataValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// JSON object
	Content       *structpb.Value `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataValue) Reset() {
	*x = DataValue{}
	mi := &file_cnquery_report_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataValue) ProtoMessage() {}

func (x *DataValue) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_report_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataValue.ProtoReflect.Descriptor instead.
func (*DataValue) Descriptor() ([]byte, []int) {
	return file_cnquery_report_proto_rawDescGZIP(), []int{3}
}

func (x *DataValue) GetContent() *structpb.Value {
	if x != nil {
		return x.Content
	}
	return nil
}

var File_cnquery_report_proto protoreflect.FileDescriptor

const file_cnquery_report_proto_rawDesc = "" +
	"\n" +
	"\x14cnquery_report.proto\x12\x18mondoo.report.cnquery.v1\x1a\x1cgoogle/protobuf/struct.proto\"\xca\x03\n" +
	"\x06Report\x12D\n" +
	"\x06assets\x18\x01 \x03(\v2,.mondoo.report.cnquery.v1.Report.AssetsEntryR\x06assets\x12>\n" +
	"\x04data\x18\x02 \x03(\v2*.mondoo.report.cnquery.v1.Report.DataEntryR\x04data\x12D\n" +
	"\x06errors\x18\x03 \x03(\v2,.mondoo.report.cnquery.v1.Report.ErrorsEntryR\x06errors\x1aZ\n" +
	"\vAssetsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.mondoo.report.cnquery.v1.AssetR\x05value:\x028\x01\x1a]\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12:\n" +
	"\x05value\x18\x02 \x01(\v2$.mondoo.report.cnquery.v1.DataValuesR\x05value:\x028\x01\x1a9\n" +
	"\vErrorsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xed\x01\n" +
	"\x05Asset\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12C\n" +
	"\x06labels\x18\x03 \x03(\v2+.mondoo.report.cnquery.v1.Asset.LabelsEntryR\x06labels\x12#\n" +
	"\rplatform_name\x18\x14 \x01(\tR\fplatformName\x12\x19\n" +
	"\btrace_id\x18\x15 \x01(\tR\atraceId\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb6\x01\n" +
	"\n" +
	"DataValues\x12H\n" +
	"\x06values\x18\x01 \x03(\v20.mondoo.report.cnquery.v1.DataValues.ValuesEntryR\x06values\x1a^\n" +
	"\vValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x129\n" +
	"\x05value\x18\x02 \x01(\v2#.mondoo.report.cnquery.v1.DataValueR\x05value:\x028\x01\"=\n" +
	"\tDataValue\x120\n" +
	"\acontent\x18\x01 \x01(\v2\x16.google.protobuf.ValueR\acontentB(Z&go.mondoo.com/cnquery/v11/cli/reporterb\x06proto3"

var (
	file_cnquery_report_proto_rawDescOnce sync.Once
	file_cnquery_report_proto_rawDescData []byte
)

func file_cnquery_report_proto_rawDescGZIP() []byte {
	file_cnquery_report_proto_rawDescOnce.Do(func() {
		file_cnquery_report_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cnquery_report_proto_rawDesc), len(file_cnquery_report_proto_rawDesc)))
	})
	return file_cnquery_report_proto_rawDescData
}

var file_cnquery_report_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_cnquery_report_proto_goTypes = []any{
	(*Report)(nil),         // 0: mondoo.report.cnquery.v1.Report
	(*Asset)(nil),          // 1: mondoo.report.cnquery.v1.Asset
	(*DataValues)(nil),     // 2: mondoo.report.cnquery.v1.DataValues
	(*DataValue)(nil),      // 3: mondoo.report.cnquery.v1.DataValue
	nil,                    // 4: mondoo.report.cnquery.v1.Report.AssetsEntry
	nil,                    // 5: mondoo.report.cnquery.v1.Report.DataEntry
	nil,                    // 6: mondoo.report.cnquery.v1.Report.ErrorsEntry
	nil,                    // 7: mondoo.report.cnquery.v1.Asset.LabelsEntry
	nil,                    // 8: mondoo.report.cnquery.v1.DataValues.ValuesEntry
	(*structpb.Value)(nil), // 9: google.protobuf.Value
}
var file_cnquery_report_proto_depIdxs = []int32{
	4, // 0: mondoo.report.cnquery.v1.Report.assets:type_name -> mondoo.report.cnquery.v1.Report.AssetsEntry
	5, // 1: mondoo.report.cnquery.v1.Report.data:type_name -> mondoo.report.cnquery.v1.Report.DataEntry
	6, // 2: mondoo.report.cnquery.v1.Report.errors:type_name -> mondoo.report.cnquery.v1.Report.ErrorsEntry
	7, // 3: mondoo.report.cnquery.v1.Asset.labels:type_name -> mondoo.report.cnquery.v1.Asset.LabelsEntry
	8, // 4: mondoo.report.cnquery.v1.DataValues.values:type_name -> mondoo.report.cnquery.v1.DataValues.ValuesEntry
	9, // 5: mondoo.report.cnquery.v1.DataValue.content:type_name -> google.protobuf.Value
	1, // 6: mondoo.report.cnquery.v1.Report.AssetsEntry.value:type_name -> mondoo.report.cnquery.v1.Asset
	2, // 7: mondoo.report.cnquery.v1.Report.DataEntry.value:type_name -> mondoo.report.cnquery.v1.DataValues
	3, // 8: mondoo.report.cnquery.v1.DataValues.ValuesEntry.value:type_name -> mondoo.report.cnquery.v1.DataValue
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_cnquery_report_proto_init() }
func file_cnquery_report_proto_init() {
	if File_cnquery_report_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cnquery_report_proto_rawDesc), len(file_cnquery_report_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cnquery_report_proto_goTypes,
		DependencyIndexes: file_cnquery_report_proto_depIdxs,
		MessageInfos:      file_cnquery_report_proto_msgTypes,
	}.Build()
	File_cnquery_report_proto = out.File
	file_cnquery_report_proto_goTypes = nil
	file_cnquery_report_proto_depIdxs = nil
}
