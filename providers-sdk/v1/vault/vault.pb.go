// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: vault.proto

package vault

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
type CredentialType int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_undefined CredentialType = 0
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_password CredentialType = 1
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_private_key CredentialType = 2
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_ssh_agent CredentialType = 3
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_bearer CredentialType = 4
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_credentials_query CredentialType = 5
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_json CredentialType = 6
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_aws_ec2_instance_connect CredentialType = 7
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_aws_ec2_ssm_session CredentialType = 8
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_pkcs12 CredentialType = 9
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	CredentialType_env CredentialType = 10
)

// Enum value maps for CredentialType.
var (
	CredentialType_name = map[int32]string{
		0:  "undefined",
		1:  "password",
		2:  "private_key",
		3:  "ssh_agent",
		4:  "bearer",
		5:  "credentials_query",
		6:  "json",
		7:  "aws_ec2_instance_connect",
		8:  "aws_ec2_ssm_session",
		9:  "pkcs12",
		10: "env",
	}
	CredentialType_value = map[string]int32{
		"undefined":                0,
		"password":                 1,
		"private_key":              2,
		"ssh_agent":                3,
		"bearer":                   4,
		"credentials_query":        5,
		"json":                     6,
		"aws_ec2_instance_connect": 7,
		"aws_ec2_ssm_session":      8,
		"pkcs12":                   9,
		"env":                      10,
	}
)

func (x CredentialType) Enum() *CredentialType {
	p := new(CredentialType)
	*p = x
	return p
}

func (x CredentialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CredentialType) Descriptor() protoreflect.EnumDescriptor {
	return file_vault_proto_enumTypes[0].Descriptor()
}

func (CredentialType) Type() protoreflect.EnumType {
	return &file_vault_proto_enumTypes[0]
}

func (x CredentialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CredentialType.Descriptor instead.
func (CredentialType) EnumDescriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{0}
}

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
type SecretEncoding int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	SecretEncoding_encoding_undefined SecretEncoding = 0
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	SecretEncoding_encoding_json SecretEncoding = 1
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	SecretEncoding_encoding_proto SecretEncoding = 2
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	SecretEncoding_encoding_binary SecretEncoding = 3
)

// Enum value maps for SecretEncoding.
var (
	SecretEncoding_name = map[int32]string{
		0: "encoding_undefined",
		1: "encoding_json",
		2: "encoding_proto",
		3: "encoding_binary",
	}
	SecretEncoding_value = map[string]int32{
		"encoding_undefined": 0,
		"encoding_json":      1,
		"encoding_proto":     2,
		"encoding_binary":    3,
	}
)

func (x SecretEncoding) Enum() *SecretEncoding {
	p := new(SecretEncoding)
	*p = x
	return p
}

func (x SecretEncoding) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecretEncoding) Descriptor() protoreflect.EnumDescriptor {
	return file_vault_proto_enumTypes[1].Descriptor()
}

func (SecretEncoding) Type() protoreflect.EnumType {
	return &file_vault_proto_enumTypes[1]
}

func (x SecretEncoding) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecretEncoding.Descriptor instead.
func (SecretEncoding) EnumDescriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{1}
}

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
type VaultType int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_None VaultType = 0
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_KeyRing VaultType = 1
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_LinuxKernelKeyring VaultType = 2
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_EncryptedFile VaultType = 3
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_HashiCorp VaultType = 4
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_GCPSecretsManager VaultType = 5
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_AWSSecretsManager VaultType = 6
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_AWSParameterStore VaultType = 7
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_GCPBerglas VaultType = 8
	// protolint:disable:next ENUM_FIELD_NAMES_UPPER_SNAKE_CASE
	VaultType_Memory VaultType = 9
)

// Enum value maps for VaultType.
var (
	VaultType_name = map[int32]string{
		0: "None",
		1: "KeyRing",
		2: "LinuxKernelKeyring",
		3: "EncryptedFile",
		4: "HashiCorp",
		5: "GCPSecretsManager",
		6: "AWSSecretsManager",
		7: "AWSParameterStore",
		8: "GCPBerglas",
		9: "Memory",
	}
	VaultType_value = map[string]int32{
		"None":               0,
		"KeyRing":            1,
		"LinuxKernelKeyring": 2,
		"EncryptedFile":      3,
		"HashiCorp":          4,
		"GCPSecretsManager":  5,
		"AWSSecretsManager":  6,
		"AWSParameterStore":  7,
		"GCPBerglas":         8,
		"Memory":             9,
	}
)

func (x VaultType) Enum() *VaultType {
	p := new(VaultType)
	*p = x
	return p
}

func (x VaultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VaultType) Descriptor() protoreflect.EnumDescriptor {
	return file_vault_proto_enumTypes[2].Descriptor()
}

func (VaultType) Type() protoreflect.EnumType {
	return &file_vault_proto_enumTypes[2]
}

func (x VaultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VaultType.Descriptor instead.
func (VaultType) EnumDescriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{2}
}

type SecretID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretID) Reset() {
	*x = SecretID{}
	mi := &file_vault_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretID) ProtoMessage() {}

func (x *SecretID) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretID.ProtoReflect.Descriptor instead.
func (*SecretID) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{0}
}

func (x *SecretID) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type Secret struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Label         string                 `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	Data          []byte                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Encoding      SecretEncoding         `protobuf:"varint,4,opt,name=encoding,proto3,enum=cnquery.providers.v1.SecretEncoding" json:"encoding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Secret) Reset() {
	*x = Secret{}
	mi := &file_vault_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Secret) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Secret) ProtoMessage() {}

func (x *Secret) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Secret.ProtoReflect.Descriptor instead.
func (*Secret) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{1}
}

func (x *Secret) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Secret) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Secret) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Secret) GetEncoding() SecretEncoding {
	if x != nil {
		return x.Encoding
	}
	return SecretEncoding_encoding_undefined
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_vault_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{2}
}

type VaultInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VaultInfo) Reset() {
	*x = VaultInfo{}
	mi := &file_vault_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VaultInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaultInfo) ProtoMessage() {}

func (x *VaultInfo) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaultInfo.ProtoReflect.Descriptor instead.
func (*VaultInfo) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{3}
}

func (x *VaultInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Credential holds authentication information
type Credential struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	SecretId string                 `protobuf:"bytes,1,opt,name=secret_id,json=secretId,proto3" json:"secret_id,omitempty"`
	Type     CredentialType         `protobuf:"varint,2,opt,name=type,proto3,enum=cnquery.providers.v1.CredentialType" json:"type,omitempty"`
	User     string                 `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Secret   []byte                 `protobuf:"bytes,4,opt,name=secret,proto3" json:"secret,omitempty"`
	// for user convenience we define password, this allows yaml/json writers
	Password string `protobuf:"bytes,21,opt,name=password,proto3" json:"password,omitempty"`
	// for user convenience we define private_key, this allows yaml/json writers
	// to just embed the string representation, otherwise it would need to be
	// base64 encoded
	PrivateKey string `protobuf:"bytes,22,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	// for user convenience we define private_key_path which loads a local file
	// into the secret
	PrivateKeyPath string `protobuf:"bytes,23,opt,name=private_key_path,json=privateKeyPath,proto3" json:"private_key_path,omitempty"`
	// for user convenience we define env_var name which loads the secret from
	// the variable
	Env           string `protobuf:"bytes,24,opt,name=env,proto3" json:"env,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Credential) Reset() {
	*x = Credential{}
	mi := &file_vault_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Credential) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Credential) ProtoMessage() {}

func (x *Credential) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Credential.ProtoReflect.Descriptor instead.
func (*Credential) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{4}
}

func (x *Credential) GetSecretId() string {
	if x != nil {
		return x.SecretId
	}
	return ""
}

func (x *Credential) GetType() CredentialType {
	if x != nil {
		return x.Type
	}
	return CredentialType_undefined
}

func (x *Credential) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Credential) GetSecret() []byte {
	if x != nil {
		return x.Secret
	}
	return nil
}

func (x *Credential) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Credential) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *Credential) GetPrivateKeyPath() string {
	if x != nil {
		return x.PrivateKeyPath
	}
	return ""
}

func (x *Credential) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

type VaultConfiguration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type          VaultType              `protobuf:"varint,2,opt,name=type,proto3,enum=cnquery.providers.v1.VaultType" json:"type,omitempty"`
	Options       map[string]string      `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VaultConfiguration) Reset() {
	*x = VaultConfiguration{}
	mi := &file_vault_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VaultConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaultConfiguration) ProtoMessage() {}

func (x *VaultConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_vault_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaultConfiguration.ProtoReflect.Descriptor instead.
func (*VaultConfiguration) Descriptor() ([]byte, []int) {
	return file_vault_proto_rawDescGZIP(), []int{5}
}

func (x *VaultConfiguration) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VaultConfiguration) GetType() VaultType {
	if x != nil {
		return x.Type
	}
	return VaultType_None
}

func (x *VaultConfiguration) GetOptions() map[string]string {
	if x != nil {
		return x.Options
	}
	return nil
}

var File_vault_proto protoreflect.FileDescriptor

const file_vault_proto_rawDesc = "" +
	"\n" +
	"\vvault.proto\x12\x14cnquery.providers.v1\"\x1c\n" +
	"\bSecretID\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\"\x86\x01\n" +
	"\x06Secret\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05label\x18\x02 \x01(\tR\x05label\x12\x12\n" +
	"\x04data\x18\x03 \x01(\fR\x04data\x12@\n" +
	"\bencoding\x18\x04 \x01(\x0e2$.cnquery.providers.v1.SecretEncodingR\bencoding\"\a\n" +
	"\x05Empty\"\x1f\n" +
	"\tVaultInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x8e\x02\n" +
	"\n" +
	"Credential\x12\x1b\n" +
	"\tsecret_id\x18\x01 \x01(\tR\bsecretId\x128\n" +
	"\x04type\x18\x02 \x01(\x0e2$.cnquery.providers.v1.CredentialTypeR\x04type\x12\x12\n" +
	"\x04user\x18\x03 \x01(\tR\x04user\x12\x16\n" +
	"\x06secret\x18\x04 \x01(\fR\x06secret\x12\x1a\n" +
	"\bpassword\x18\x15 \x01(\tR\bpassword\x12\x1f\n" +
	"\vprivate_key\x18\x16 \x01(\tR\n" +
	"privateKey\x12(\n" +
	"\x10private_key_path\x18\x17 \x01(\tR\x0eprivateKeyPath\x12\x10\n" +
	"\x03env\x18\x18 \x01(\tR\x03envJ\x04\b\x05\x10\x06\"\xea\x01\n" +
	"\x12VaultConfiguration\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x123\n" +
	"\x04type\x18\x02 \x01(\x0e2\x1f.cnquery.providers.v1.VaultTypeR\x04type\x12O\n" +
	"\aoptions\x18\x03 \x03(\v25.cnquery.providers.v1.VaultConfiguration.OptionsEntryR\aoptions\x1a:\n" +
	"\fOptionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*\xc6\x01\n" +
	"\x0eCredentialType\x12\r\n" +
	"\tundefined\x10\x00\x12\f\n" +
	"\bpassword\x10\x01\x12\x0f\n" +
	"\vprivate_key\x10\x02\x12\r\n" +
	"\tssh_agent\x10\x03\x12\n" +
	"\n" +
	"\x06bearer\x10\x04\x12\x15\n" +
	"\x11credentials_query\x10\x05\x12\b\n" +
	"\x04json\x10\x06\x12\x1c\n" +
	"\x18aws_ec2_instance_connect\x10\a\x12\x17\n" +
	"\x13aws_ec2_ssm_session\x10\b\x12\n" +
	"\n" +
	"\x06pkcs12\x10\t\x12\a\n" +
	"\x03env\x10\n" +
	"*d\n" +
	"\x0eSecretEncoding\x12\x16\n" +
	"\x12encoding_undefined\x10\x00\x12\x11\n" +
	"\rencoding_json\x10\x01\x12\x12\n" +
	"\x0eencoding_proto\x10\x02\x12\x13\n" +
	"\x0fencoding_binary\x10\x03*\xbd\x01\n" +
	"\tVaultType\x12\b\n" +
	"\x04None\x10\x00\x12\v\n" +
	"\aKeyRing\x10\x01\x12\x16\n" +
	"\x12LinuxKernelKeyring\x10\x02\x12\x11\n" +
	"\rEncryptedFile\x10\x03\x12\r\n" +
	"\tHashiCorp\x10\x04\x12\x15\n" +
	"\x11GCPSecretsManager\x10\x05\x12\x15\n" +
	"\x11AWSSecretsManager\x10\x06\x12\x15\n" +
	"\x11AWSParameterStore\x10\a\x12\x0e\n" +
	"\n" +
	"GCPBerglas\x10\b\x12\n" +
	"\n" +
	"\x06Memory\x10\t2\xd8\x01\n" +
	"\x05Vault\x12E\n" +
	"\x05About\x12\x1b.cnquery.providers.v1.Empty\x1a\x1f.cnquery.providers.v1.VaultInfo\x12C\n" +
	"\x03Get\x12\x1e.cnquery.providers.v1.SecretID\x1a\x1c.cnquery.providers.v1.Secret\x12C\n" +
	"\x03Set\x12\x1c.cnquery.providers.v1.Secret\x1a\x1e.cnquery.providers.v1.SecretIDB2Z0go.mondoo.com/cnquery/v11/providers-sdk/v1/vaultb\x06proto3"

var (
	file_vault_proto_rawDescOnce sync.Once
	file_vault_proto_rawDescData []byte
)

func file_vault_proto_rawDescGZIP() []byte {
	file_vault_proto_rawDescOnce.Do(func() {
		file_vault_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vault_proto_rawDesc), len(file_vault_proto_rawDesc)))
	})
	return file_vault_proto_rawDescData
}

var file_vault_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_vault_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_vault_proto_goTypes = []any{
	(CredentialType)(0),        // 0: cnquery.providers.v1.CredentialType
	(SecretEncoding)(0),        // 1: cnquery.providers.v1.SecretEncoding
	(VaultType)(0),             // 2: cnquery.providers.v1.VaultType
	(*SecretID)(nil),           // 3: cnquery.providers.v1.SecretID
	(*Secret)(nil),             // 4: cnquery.providers.v1.Secret
	(*Empty)(nil),              // 5: cnquery.providers.v1.Empty
	(*VaultInfo)(nil),          // 6: cnquery.providers.v1.VaultInfo
	(*Credential)(nil),         // 7: cnquery.providers.v1.Credential
	(*VaultConfiguration)(nil), // 8: cnquery.providers.v1.VaultConfiguration
	nil,                        // 9: cnquery.providers.v1.VaultConfiguration.OptionsEntry
}
var file_vault_proto_depIdxs = []int32{
	1, // 0: cnquery.providers.v1.Secret.encoding:type_name -> cnquery.providers.v1.SecretEncoding
	0, // 1: cnquery.providers.v1.Credential.type:type_name -> cnquery.providers.v1.CredentialType
	2, // 2: cnquery.providers.v1.VaultConfiguration.type:type_name -> cnquery.providers.v1.VaultType
	9, // 3: cnquery.providers.v1.VaultConfiguration.options:type_name -> cnquery.providers.v1.VaultConfiguration.OptionsEntry
	5, // 4: cnquery.providers.v1.Vault.About:input_type -> cnquery.providers.v1.Empty
	3, // 5: cnquery.providers.v1.Vault.Get:input_type -> cnquery.providers.v1.SecretID
	4, // 6: cnquery.providers.v1.Vault.Set:input_type -> cnquery.providers.v1.Secret
	6, // 7: cnquery.providers.v1.Vault.About:output_type -> cnquery.providers.v1.VaultInfo
	4, // 8: cnquery.providers.v1.Vault.Get:output_type -> cnquery.providers.v1.Secret
	3, // 9: cnquery.providers.v1.Vault.Set:output_type -> cnquery.providers.v1.SecretID
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_vault_proto_init() }
func file_vault_proto_init() {
	if File_vault_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vault_proto_rawDesc), len(file_vault_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vault_proto_goTypes,
		DependencyIndexes: file_vault_proto_depIdxs,
		EnumInfos:         file_vault_proto_enumTypes,
		MessageInfos:      file_vault_proto_msgTypes,
	}.Build()
	File_vault_proto = out.File
	file_vault_proto_goTypes = nil
	file_vault_proto_depIdxs = nil
}
