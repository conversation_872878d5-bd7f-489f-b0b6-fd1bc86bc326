// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: resources.proto

package resources

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Schema struct {
	state     protoimpl.MessageState   `protogen:"open.v1"`
	Resources map[string]*ResourceInfo `protobuf:"bytes,3,rep,name=resources,proto3" json:"resources,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Resources can depend on resources from another provider,
	// this is the list of of providers the schema depends on.
	Dependencies  map[string]*ProviderInfo `protobuf:"bytes,4,rep,name=dependencies,proto3" json:"dependencies,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Schema) Reset() {
	*x = Schema{}
	mi := &file_resources_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{0}
}

func (x *Schema) GetResources() map[string]*ResourceInfo {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Schema) GetDependencies() map[string]*ProviderInfo {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

type ProviderInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProviderInfo) Reset() {
	*x = ProviderInfo{}
	mi := &file_resources_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProviderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProviderInfo) ProtoMessage() {}

func (x *ProviderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProviderInfo.ProtoReflect.Descriptor instead.
func (*ProviderInfo) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{1}
}

func (x *ProviderInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProviderInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ResourceID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceID) Reset() {
	*x = ResourceID{}
	mi := &file_resources_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceID) ProtoMessage() {}

func (x *ResourceID) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceID.ProtoReflect.Descriptor instead.
func (*ResourceID) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{2}
}

func (x *ResourceID) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResourceID) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TypedArg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Optional      bool                   `protobuf:"varint,3,opt,name=optional,proto3" json:"optional,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TypedArg) Reset() {
	*x = TypedArg{}
	mi := &file_resources_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TypedArg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypedArg) ProtoMessage() {}

func (x *TypedArg) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypedArg.ProtoReflect.Descriptor instead.
func (*TypedArg) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{3}
}

func (x *TypedArg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TypedArg) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TypedArg) GetOptional() bool {
	if x != nil {
		return x.Optional
	}
	return false
}

type Init struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Args          []*TypedArg            `protobuf:"bytes,1,rep,name=args,proto3" json:"args,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Init) Reset() {
	*x = Init{}
	mi := &file_resources_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Init) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Init) ProtoMessage() {}

func (x *Init) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Init.ProtoReflect.Descriptor instead.
func (*Init) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{4}
}

func (x *Init) GetArgs() []*TypedArg {
	if x != nil {
		return x.Args
	}
	return nil
}

type ResourceInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Fields           map[string]*Field      `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Init             *Init                  `protobuf:"bytes,20,opt,name=init,proto3" json:"init,omitempty"`
	ListType         string                 `protobuf:"bytes,21,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Title            string                 `protobuf:"bytes,22,opt,name=title,proto3" json:"title,omitempty"`
	Desc             string                 `protobuf:"bytes,23,opt,name=desc,proto3" json:"desc,omitempty"`
	Private          bool                   `protobuf:"varint,24,opt,name=private,proto3" json:"private,omitempty"`
	IsExtension      bool                   `protobuf:"varint,28,opt,name=is_extension,json=isExtension,proto3" json:"is_extension,omitempty"`
	MinMondooVersion string                 `protobuf:"bytes,25,opt,name=min_mondoo_version,json=minMondooVersion,proto3" json:"min_mondoo_version,omitempty"`
	Defaults         string                 `protobuf:"bytes,26,opt,name=defaults,proto3" json:"defaults,omitempty"`
	Context          string                 `protobuf:"bytes,30,opt,name=context,proto3" json:"context,omitempty"`
	Provider         string                 `protobuf:"bytes,27,opt,name=provider,proto3" json:"provider,omitempty"`
	// This field contains references to other providers with the same
	// resource/field.
	// Note: Please do not use this field, it is only temporary and will be
	// removed in the future once binding resources are mandatory for all
	// executions.
	Others        []*ResourceInfo `protobuf:"bytes,29,rep,name=others,proto3" json:"others,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceInfo) Reset() {
	*x = ResourceInfo{}
	mi := &file_resources_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceInfo) ProtoMessage() {}

func (x *ResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceInfo.ProtoReflect.Descriptor instead.
func (*ResourceInfo) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{5}
}

func (x *ResourceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResourceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceInfo) GetFields() map[string]*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *ResourceInfo) GetInit() *Init {
	if x != nil {
		return x.Init
	}
	return nil
}

func (x *ResourceInfo) GetListType() string {
	if x != nil {
		return x.ListType
	}
	return ""
}

func (x *ResourceInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ResourceInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ResourceInfo) GetPrivate() bool {
	if x != nil {
		return x.Private
	}
	return false
}

func (x *ResourceInfo) GetIsExtension() bool {
	if x != nil {
		return x.IsExtension
	}
	return false
}

func (x *ResourceInfo) GetMinMondooVersion() string {
	if x != nil {
		return x.MinMondooVersion
	}
	return ""
}

func (x *ResourceInfo) GetDefaults() string {
	if x != nil {
		return x.Defaults
	}
	return ""
}

func (x *ResourceInfo) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *ResourceInfo) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *ResourceInfo) GetOthers() []*ResourceInfo {
	if x != nil {
		return x.Others
	}
	return nil
}

type Field struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Name               string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type               string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	IsMandatory        bool                   `protobuf:"varint,3,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	Refs               []string               `protobuf:"bytes,4,rep,name=refs,proto3" json:"refs,omitempty"`
	Title              string                 `protobuf:"bytes,20,opt,name=title,proto3" json:"title,omitempty"`
	Desc               string                 `protobuf:"bytes,21,opt,name=desc,proto3" json:"desc,omitempty"`
	IsPrivate          bool                   `protobuf:"varint,22,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	MinMondooVersion   string                 `protobuf:"bytes,23,opt,name=min_mondoo_version,json=minMondooVersion,proto3" json:"min_mondoo_version,omitempty"`
	IsImplicitResource bool                   `protobuf:"varint,24,opt,name=is_implicit_resource,json=isImplicitResource,proto3" json:"is_implicit_resource,omitempty"`
	IsEmbedded         bool                   `protobuf:"varint,25,opt,name=is_embedded,json=isEmbedded,proto3" json:"is_embedded,omitempty"`
	Provider           string                 `protobuf:"bytes,27,opt,name=provider,proto3" json:"provider,omitempty"`
	// This field contains references to other providers with the same
	// resource/field.
	// Note: Please do not use this field, it is only temporary and will be
	// removed in the future once binding resources are mandatory for all
	// executions.
	Others        []*Field `protobuf:"bytes,29,rep,name=others,proto3" json:"others,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Field) Reset() {
	*x = Field{}
	mi := &file_resources_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_resources_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_resources_proto_rawDescGZIP(), []int{6}
}

func (x *Field) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Field) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Field) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *Field) GetRefs() []string {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *Field) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Field) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Field) GetIsPrivate() bool {
	if x != nil {
		return x.IsPrivate
	}
	return false
}

func (x *Field) GetMinMondooVersion() string {
	if x != nil {
		return x.MinMondooVersion
	}
	return ""
}

func (x *Field) GetIsImplicitResource() bool {
	if x != nil {
		return x.IsImplicitResource
	}
	return false
}

func (x *Field) GetIsEmbedded() bool {
	if x != nil {
		return x.IsEmbedded
	}
	return false
}

func (x *Field) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Field) GetOthers() []*Field {
	if x != nil {
		return x.Others
	}
	return nil
}

var File_resources_proto protoreflect.FileDescriptor

const file_resources_proto_rawDesc = "" +
	"\n" +
	"\x0fresources.proto\x12\x10mondoo.resources\"\xde\x02\n" +
	"\x06Schema\x12E\n" +
	"\tresources\x18\x03 \x03(\v2'.mondoo.resources.Schema.ResourcesEntryR\tresources\x12N\n" +
	"\fdependencies\x18\x04 \x03(\v2*.mondoo.resources.Schema.DependenciesEntryR\fdependencies\x1a\\\n" +
	"\x0eResourcesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x124\n" +
	"\x05value\x18\x02 \x01(\v2\x1e.mondoo.resources.ResourceInfoR\x05value:\x028\x01\x1a_\n" +
	"\x11DependenciesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x124\n" +
	"\x05value\x18\x02 \x01(\v2\x1e.mondoo.resources.ProviderInfoR\x05value:\x028\x01\"2\n" +
	"\fProviderInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"0\n" +
	"\n" +
	"ResourceID\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"N\n" +
	"\bTypedArg\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x1a\n" +
	"\boptional\x18\x03 \x01(\bR\boptional\"6\n" +
	"\x04Init\x12.\n" +
	"\x04args\x18\x01 \x03(\v2\x1a.mondoo.resources.TypedArgR\x04args\"\xb2\x04\n" +
	"\fResourceInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12B\n" +
	"\x06fields\x18\x03 \x03(\v2*.mondoo.resources.ResourceInfo.FieldsEntryR\x06fields\x12*\n" +
	"\x04init\x18\x14 \x01(\v2\x16.mondoo.resources.InitR\x04init\x12\x1b\n" +
	"\tlist_type\x18\x15 \x01(\tR\blistType\x12\x14\n" +
	"\x05title\x18\x16 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x17 \x01(\tR\x04desc\x12\x18\n" +
	"\aprivate\x18\x18 \x01(\bR\aprivate\x12!\n" +
	"\fis_extension\x18\x1c \x01(\bR\visExtension\x12,\n" +
	"\x12min_mondoo_version\x18\x19 \x01(\tR\x10minMondooVersion\x12\x1a\n" +
	"\bdefaults\x18\x1a \x01(\tR\bdefaults\x12\x18\n" +
	"\acontext\x18\x1e \x01(\tR\acontext\x12\x1a\n" +
	"\bprovider\x18\x1b \x01(\tR\bprovider\x126\n" +
	"\x06others\x18\x1d \x03(\v2\x1e.mondoo.resources.ResourceInfoR\x06others\x1aR\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.mondoo.resources.FieldR\x05value:\x028\x01\"\xfd\x02\n" +
	"\x05Field\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12!\n" +
	"\fis_mandatory\x18\x03 \x01(\bR\visMandatory\x12\x12\n" +
	"\x04refs\x18\x04 \x03(\tR\x04refs\x12\x14\n" +
	"\x05title\x18\x14 \x01(\tR\x05title\x12\x12\n" +
	"\x04desc\x18\x15 \x01(\tR\x04desc\x12\x1d\n" +
	"\n" +
	"is_private\x18\x16 \x01(\bR\tisPrivate\x12,\n" +
	"\x12min_mondoo_version\x18\x17 \x01(\tR\x10minMondooVersion\x120\n" +
	"\x14is_implicit_resource\x18\x18 \x01(\bR\x12isImplicitResource\x12\x1f\n" +
	"\vis_embedded\x18\x19 \x01(\bR\n" +
	"isEmbedded\x12\x1a\n" +
	"\bprovider\x18\x1b \x01(\tR\bprovider\x12/\n" +
	"\x06others\x18\x1d \x03(\v2\x17.mondoo.resources.FieldR\x06othersB6Z4go.mondoo.com/cnquery/v11/providers-sdk/v1/resourcesb\x06proto3"

var (
	file_resources_proto_rawDescOnce sync.Once
	file_resources_proto_rawDescData []byte
)

func file_resources_proto_rawDescGZIP() []byte {
	file_resources_proto_rawDescOnce.Do(func() {
		file_resources_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_resources_proto_rawDesc), len(file_resources_proto_rawDesc)))
	})
	return file_resources_proto_rawDescData
}

var file_resources_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_resources_proto_goTypes = []any{
	(*Schema)(nil),       // 0: mondoo.resources.Schema
	(*ProviderInfo)(nil), // 1: mondoo.resources.ProviderInfo
	(*ResourceID)(nil),   // 2: mondoo.resources.ResourceID
	(*TypedArg)(nil),     // 3: mondoo.resources.TypedArg
	(*Init)(nil),         // 4: mondoo.resources.Init
	(*ResourceInfo)(nil), // 5: mondoo.resources.ResourceInfo
	(*Field)(nil),        // 6: mondoo.resources.Field
	nil,                  // 7: mondoo.resources.Schema.ResourcesEntry
	nil,                  // 8: mondoo.resources.Schema.DependenciesEntry
	nil,                  // 9: mondoo.resources.ResourceInfo.FieldsEntry
}
var file_resources_proto_depIdxs = []int32{
	7,  // 0: mondoo.resources.Schema.resources:type_name -> mondoo.resources.Schema.ResourcesEntry
	8,  // 1: mondoo.resources.Schema.dependencies:type_name -> mondoo.resources.Schema.DependenciesEntry
	3,  // 2: mondoo.resources.Init.args:type_name -> mondoo.resources.TypedArg
	9,  // 3: mondoo.resources.ResourceInfo.fields:type_name -> mondoo.resources.ResourceInfo.FieldsEntry
	4,  // 4: mondoo.resources.ResourceInfo.init:type_name -> mondoo.resources.Init
	5,  // 5: mondoo.resources.ResourceInfo.others:type_name -> mondoo.resources.ResourceInfo
	6,  // 6: mondoo.resources.Field.others:type_name -> mondoo.resources.Field
	5,  // 7: mondoo.resources.Schema.ResourcesEntry.value:type_name -> mondoo.resources.ResourceInfo
	1,  // 8: mondoo.resources.Schema.DependenciesEntry.value:type_name -> mondoo.resources.ProviderInfo
	6,  // 9: mondoo.resources.ResourceInfo.FieldsEntry.value:type_name -> mondoo.resources.Field
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_resources_proto_init() }
func file_resources_proto_init() {
	if File_resources_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_resources_proto_rawDesc), len(file_resources_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_resources_proto_goTypes,
		DependencyIndexes: file_resources_proto_depIdxs,
		MessageInfos:      file_resources_proto_msgTypes,
	}.Build()
	File_resources_proto = out.File
	file_resources_proto_goTypes = nil
	file_resources_proto_depIdxs = nil
}
