// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: inventory.proto

package inventory

import (
	upstream "go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream"
	vault "go.mondoo.com/cnquery/v11/providers-sdk/v1/vault"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type State int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	State_STATE_UNKNOWN State = 0
	// eg. permission or io error
	State_STATE_ERROR State = 1
	// run time states
	State_STATE_PENDING    State = 2
	State_STATE_RUNNING    State = 3
	State_STATE_STOPPING   State = 4
	State_STATE_STOPPED    State = 5
	State_STATE_SHUTDOWN   State = 6
	State_STATE_TERMINATED State = 7
	State_STATE_REBOOT     State = 8
	// static states
	State_STATE_ONLINE  State = 9
	State_STATE_OFFLINE State = 10
	// the asset is marked as deleted
	State_STATE_DELETED State = 11
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0:  "STATE_UNKNOWN",
		1:  "STATE_ERROR",
		2:  "STATE_PENDING",
		3:  "STATE_RUNNING",
		4:  "STATE_STOPPING",
		5:  "STATE_STOPPED",
		6:  "STATE_SHUTDOWN",
		7:  "STATE_TERMINATED",
		8:  "STATE_REBOOT",
		9:  "STATE_ONLINE",
		10: "STATE_OFFLINE",
		11: "STATE_DELETED",
	}
	State_value = map[string]int32{
		"STATE_UNKNOWN":    0,
		"STATE_ERROR":      1,
		"STATE_PENDING":    2,
		"STATE_RUNNING":    3,
		"STATE_STOPPING":   4,
		"STATE_STOPPED":    5,
		"STATE_SHUTDOWN":   6,
		"STATE_TERMINATED": 7,
		"STATE_REBOOT":     8,
		"STATE_ONLINE":     9,
		"STATE_OFFLINE":    10,
		"STATE_DELETED":    11,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_inventory_proto_enumTypes[0].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_inventory_proto_enumTypes[0]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{0}
}

type AssetCategory int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	AssetCategory_CATEGORY_INVENTORY AssetCategory = 0
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	AssetCategory_CATEGORY_CICD AssetCategory = 1
)

// Enum value maps for AssetCategory.
var (
	AssetCategory_name = map[int32]string{
		0: "CATEGORY_INVENTORY",
		1: "CATEGORY_CICD",
	}
	AssetCategory_value = map[string]int32{
		"CATEGORY_INVENTORY": 0,
		"CATEGORY_CICD":      1,
	}
)

func (x AssetCategory) Enum() *AssetCategory {
	p := new(AssetCategory)
	*p = x
	return p
}

func (x AssetCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_inventory_proto_enumTypes[1].Descriptor()
}

func (AssetCategory) Type() protoreflect.EnumType {
	return &file_inventory_proto_enumTypes[1]
}

func (x AssetCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetCategory.Descriptor instead.
func (AssetCategory) EnumDescriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{1}
}

// FIXME: DEPRECATED, remove in v10.0 (or later) vv
type ProviderType int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_LOCAL_OS ProviderType = 0
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_DOCKER_ENGINE_IMAGE ProviderType = 1
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_DOCKER_ENGINE_CONTAINER ProviderType = 2
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_SSH ProviderType = 3
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_WINRM ProviderType = 4
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_AWS_SSM_RUN_COMMAND ProviderType = 5
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_CONTAINER_REGISTRY ProviderType = 6
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_TAR ProviderType = 7
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_MOCK ProviderType = 8
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_VSPHERE ProviderType = 9
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_ARISTAEOS ProviderType = 10
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_AWS ProviderType = 12
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_GCP ProviderType = 13
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_AZURE ProviderType = 14
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_MS365 ProviderType = 15
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_IPMI ProviderType = 16
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_VSPHERE_VM ProviderType = 17
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_FS ProviderType = 18
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_K8S ProviderType = 19
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_EQUINIX_METAL ProviderType = 20
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_DOCKER ProviderType = 21 // unspecified if this is a container or image
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_GITHUB ProviderType = 22
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_VAGRANT ProviderType = 23
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_AWS_EC2_EBS ProviderType = 24
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_GITLAB ProviderType = 25
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_TERRAFORM ProviderType = 26
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_HOST ProviderType = 27
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_UNKNOWN ProviderType = 28
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_OKTA ProviderType = 29
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_GOOGLE_WORKSPACE ProviderType = 30
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_SLACK ProviderType = 31
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_VCD ProviderType = 32
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_OCI ProviderType = 33
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_OPCUA ProviderType = 34
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	ProviderType_GCP_COMPUTE_INSTANCE_SNAPSHOT ProviderType = 35
)

// Enum value maps for ProviderType.
var (
	ProviderType_name = map[int32]string{
		0:  "LOCAL_OS",
		1:  "DOCKER_ENGINE_IMAGE",
		2:  "DOCKER_ENGINE_CONTAINER",
		3:  "SSH",
		4:  "WINRM",
		5:  "AWS_SSM_RUN_COMMAND",
		6:  "CONTAINER_REGISTRY",
		7:  "TAR",
		8:  "MOCK",
		9:  "VSPHERE",
		10: "ARISTAEOS",
		12: "AWS",
		13: "GCP",
		14: "AZURE",
		15: "MS365",
		16: "IPMI",
		17: "VSPHERE_VM",
		18: "FS",
		19: "K8S",
		20: "EQUINIX_METAL",
		21: "DOCKER",
		22: "GITHUB",
		23: "VAGRANT",
		24: "AWS_EC2_EBS",
		25: "GITLAB",
		26: "TERRAFORM",
		27: "HOST",
		28: "UNKNOWN",
		29: "OKTA",
		30: "GOOGLE_WORKSPACE",
		31: "SLACK",
		32: "VCD",
		33: "OCI",
		34: "OPCUA",
		35: "GCP_COMPUTE_INSTANCE_SNAPSHOT",
	}
	ProviderType_value = map[string]int32{
		"LOCAL_OS":                      0,
		"DOCKER_ENGINE_IMAGE":           1,
		"DOCKER_ENGINE_CONTAINER":       2,
		"SSH":                           3,
		"WINRM":                         4,
		"AWS_SSM_RUN_COMMAND":           5,
		"CONTAINER_REGISTRY":            6,
		"TAR":                           7,
		"MOCK":                          8,
		"VSPHERE":                       9,
		"ARISTAEOS":                     10,
		"AWS":                           12,
		"GCP":                           13,
		"AZURE":                         14,
		"MS365":                         15,
		"IPMI":                          16,
		"VSPHERE_VM":                    17,
		"FS":                            18,
		"K8S":                           19,
		"EQUINIX_METAL":                 20,
		"DOCKER":                        21,
		"GITHUB":                        22,
		"VAGRANT":                       23,
		"AWS_EC2_EBS":                   24,
		"GITLAB":                        25,
		"TERRAFORM":                     26,
		"HOST":                          27,
		"UNKNOWN":                       28,
		"OKTA":                          29,
		"GOOGLE_WORKSPACE":              30,
		"SLACK":                         31,
		"VCD":                           32,
		"OCI":                           33,
		"OPCUA":                         34,
		"GCP_COMPUTE_INSTANCE_SNAPSHOT": 35,
	}
)

func (x ProviderType) Enum() *ProviderType {
	p := new(ProviderType)
	*p = x
	return p
}

func (x ProviderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProviderType) Descriptor() protoreflect.EnumDescriptor {
	return file_inventory_proto_enumTypes[2].Descriptor()
}

func (ProviderType) Type() protoreflect.EnumType {
	return &file_inventory_proto_enumTypes[2]
}

func (x ProviderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProviderType.Descriptor instead.
func (ProviderType) EnumDescriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{2}
}

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
// protolint:disable:next ENUM_NAMES_UPPER_CAMEL_CASE
type DeprecatedV8_Kind int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_UNKNOWN DeprecatedV8_Kind = 0
	// at rest
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_VIRTUAL_MACHINE_IMAGE DeprecatedV8_Kind = 1
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_CONTAINER_IMAGE DeprecatedV8_Kind = 2
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_CODE DeprecatedV8_Kind = 3
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_PACKAGE DeprecatedV8_Kind = 4
	// in motion
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_VIRTUAL_MACHINE DeprecatedV8_Kind = 5
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_CONTAINER DeprecatedV8_Kind = 6
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_PROCESS DeprecatedV8_Kind = 7
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_API DeprecatedV8_Kind = 8
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_BARE_METAL DeprecatedV8_Kind = 9
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_NETWORK DeprecatedV8_Kind = 10
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_K8S_OBJECT DeprecatedV8_Kind = 11
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_AWS_OBJECT DeprecatedV8_Kind = 12
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_GCP_OBJECT DeprecatedV8_Kind = 13
	// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
	DeprecatedV8_Kind_KIND_AZURE_OBJECT DeprecatedV8_Kind = 14
)

// Enum value maps for DeprecatedV8_Kind.
var (
	DeprecatedV8_Kind_name = map[int32]string{
		0:  "KIND_UNKNOWN",
		1:  "KIND_VIRTUAL_MACHINE_IMAGE",
		2:  "KIND_CONTAINER_IMAGE",
		3:  "KIND_CODE",
		4:  "KIND_PACKAGE",
		5:  "KIND_VIRTUAL_MACHINE",
		6:  "KIND_CONTAINER",
		7:  "KIND_PROCESS",
		8:  "KIND_API",
		9:  "KIND_BARE_METAL",
		10: "KIND_NETWORK",
		11: "KIND_K8S_OBJECT",
		12: "KIND_AWS_OBJECT",
		13: "KIND_GCP_OBJECT",
		14: "KIND_AZURE_OBJECT",
	}
	DeprecatedV8_Kind_value = map[string]int32{
		"KIND_UNKNOWN":               0,
		"KIND_VIRTUAL_MACHINE_IMAGE": 1,
		"KIND_CONTAINER_IMAGE":       2,
		"KIND_CODE":                  3,
		"KIND_PACKAGE":               4,
		"KIND_VIRTUAL_MACHINE":       5,
		"KIND_CONTAINER":             6,
		"KIND_PROCESS":               7,
		"KIND_API":                   8,
		"KIND_BARE_METAL":            9,
		"KIND_NETWORK":               10,
		"KIND_K8S_OBJECT":            11,
		"KIND_AWS_OBJECT":            12,
		"KIND_GCP_OBJECT":            13,
		"KIND_AZURE_OBJECT":          14,
	}
)

func (x DeprecatedV8_Kind) Enum() *DeprecatedV8_Kind {
	p := new(DeprecatedV8_Kind)
	*p = x
	return p
}

func (x DeprecatedV8_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeprecatedV8_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_inventory_proto_enumTypes[3].Descriptor()
}

func (DeprecatedV8_Kind) Type() protoreflect.EnumType {
	return &file_inventory_proto_enumTypes[3]
}

func (x DeprecatedV8_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeprecatedV8_Kind.Descriptor instead.
func (DeprecatedV8_Kind) EnumDescriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{3}
}

type Asset struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Mrn   string                 `protobuf:"bytes,2,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Name  string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 3rd-party platform id eg. amazon arn, gcp resource name or ssh host key
	PlatformIds []string `protobuf:"bytes,4,rep,name=platform_ids,json=platformIds,proto3" json:"platform_ids,omitempty"`
	// asset state
	State    State     `protobuf:"varint,5,opt,name=state,proto3,enum=cnquery.providers.v1.State" json:"state,omitempty"`
	Platform *Platform `protobuf:"bytes,6,opt,name=platform,proto3" json:"platform,omitempty"`
	// key is a lower case string of connection type
	Connections []*Config `protobuf:"bytes,17,rep,name=connections,proto3" json:"connections,omitempty"`
	// labeled assets can be searched by labels
	Labels map[string]string `protobuf:"bytes,18,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// additional information that is not touched by the system
	Annotations map[string]string `protobuf:"bytes,19,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// additional options for that asset
	Options map[string]string `protobuf:"bytes,20,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// platform id detection mechanisms
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	IdDetector []string `protobuf:"bytes,31,rep,name=id_detector,json=idDetector,proto3" json:"id_detector,omitempty"`
	// indicator is this is an inventory object or a CI/CD run
	Category      AssetCategory `protobuf:"varint,32,opt,name=category,proto3,enum=cnquery.providers.v1.AssetCategory" json:"category,omitempty"`
	RelatedAssets []*Asset      `protobuf:"bytes,33,rep,name=related_assets,json=relatedAssets,proto3" json:"related_assets,omitempty"`
	ManagedBy     string        `protobuf:"bytes,34,opt,name=managed_by,json=managedBy,proto3" json:"managed_by,omitempty"`
	// optional url that can be used to access the asset via a browser
	Url           string `protobuf:"bytes,35,opt,name=url,proto3" json:"url,omitempty"`
	KindString    string `protobuf:"bytes,36,opt,name=kind_string,json=kindString,proto3" json:"kind_string,omitempty"`
	Fqdn          string `protobuf:"bytes,37,opt,name=fqdn,proto3" json:"fqdn,omitempty"`
	TraceId       string `protobuf:"bytes,38,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Asset) Reset() {
	*x = Asset{}
	mi := &file_inventory_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Asset) ProtoMessage() {}

func (x *Asset) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Asset.ProtoReflect.Descriptor instead.
func (*Asset) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{0}
}

func (x *Asset) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Asset) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Asset) GetPlatformIds() []string {
	if x != nil {
		return x.PlatformIds
	}
	return nil
}

func (x *Asset) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNKNOWN
}

func (x *Asset) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *Asset) GetConnections() []*Config {
	if x != nil {
		return x.Connections
	}
	return nil
}

func (x *Asset) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Asset) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Asset) GetOptions() map[string]string {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Asset) GetIdDetector() []string {
	if x != nil {
		return x.IdDetector
	}
	return nil
}

func (x *Asset) GetCategory() AssetCategory {
	if x != nil {
		return x.Category
	}
	return AssetCategory_CATEGORY_INVENTORY
}

func (x *Asset) GetRelatedAssets() []*Asset {
	if x != nil {
		return x.RelatedAssets
	}
	return nil
}

func (x *Asset) GetManagedBy() string {
	if x != nil {
		return x.ManagedBy
	}
	return ""
}

func (x *Asset) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Asset) GetKindString() string {
	if x != nil {
		return x.KindString
	}
	return ""
}

func (x *Asset) GetFqdn() string {
	if x != nil {
		return x.Fqdn
	}
	return ""
}

func (x *Asset) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// AssetUrlBranch defines the hierarchy into which an asset can be placed. It
// makes it easier to find and group assets. Typically this is a subset of all
// possible asset relationships used to generate an opinionated view on an
// asset.
//
// AssetUrlBranches are part of the overall AssetUrlSchema, to which they are
// attached.
type AssetUrlBranch struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The path to which this tree is getting attached. Only necessary for the
	// top-most branches, not necessary for branches inside the values field.
	PathSegments []string `protobuf:"bytes,1,rep,name=path_segments,json=pathSegments,proto3" json:"path_segments,omitempty"`
	// key of this tree. Every tree must have one key only at its root.
	// Must be [a-z0-9_-]+ up to 100 characters
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// values of this tree. Other trees can attach themselves into this without
	// overwriting existing values. Must be [A-Za-z0-9_-]+ up to 200 characters
	// The special value '*' is used to designate arbitrary values.
	Values map[string]*AssetUrlBranch `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// title for pretty-printing this branch
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// reference to other subtree that will be used at this position. Allows
	// providers to re-use url parts from other definitions. For example:
	// attaching the /technology=os subtree to cloud VMs
	References []string `protobuf:"bytes,5,rep,name=references,proto3" json:"references,omitempty"`
	// internal only: depth of the subtree
	Depth uint32 `protobuf:"varint,20,opt,name=depth,proto3" json:"depth,omitempty"`
	// internal only: parent relationships
	Parent *AssetUrlBranch `protobuf:"bytes,21,opt,name=parent,proto3" json:"parent,omitempty"`
	// internal only: how this branch is connected in the parent's value field
	ParentValue   string `protobuf:"bytes,22,opt,name=parent_value,json=parentValue,proto3" json:"parent_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssetUrlBranch) Reset() {
	*x = AssetUrlBranch{}
	mi := &file_inventory_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssetUrlBranch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetUrlBranch) ProtoMessage() {}

func (x *AssetUrlBranch) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetUrlBranch.ProtoReflect.Descriptor instead.
func (*AssetUrlBranch) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{1}
}

func (x *AssetUrlBranch) GetPathSegments() []string {
	if x != nil {
		return x.PathSegments
	}
	return nil
}

func (x *AssetUrlBranch) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AssetUrlBranch) GetValues() map[string]*AssetUrlBranch {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *AssetUrlBranch) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AssetUrlBranch) GetReferences() []string {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *AssetUrlBranch) GetDepth() uint32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

func (x *AssetUrlBranch) GetParent() *AssetUrlBranch {
	if x != nil {
		return x.Parent
	}
	return nil
}

func (x *AssetUrlBranch) GetParentValue() string {
	if x != nil {
		return x.ParentValue
	}
	return ""
}

type Config struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// FIXME: DEPRECATED, remove in v10.0 (or later) vv
	// This is replaced by type. We use a different number here so it doesn't
	// conflict with the old "backend" while allowing us to load the field from
	// yaml.
	Backend ProviderType      `protobuf:"varint,28,opt,name=backend,proto3,enum=cnquery.providers.v1.ProviderType" json:"backend,omitempty"`
	Kind    DeprecatedV8_Kind `protobuf:"varint,24,opt,name=kind,proto3,enum=cnquery.providers.v1.DeprecatedV8_Kind" json:"kind,omitempty"` // ^^
	Host    string            `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	// Ports are not int by default, eg. docker://centos:latest parses a string
	// as port. Therefore it is up to the provider to convert the port to what
	// they need
	Port               int32  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Path               string `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	Id                 uint32 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	ParentConnectionId uint32 `protobuf:"varint,30,opt,name=parent_connection_id,json=parentConnectionId,proto3" json:"parent_connection_id,omitempty"`
	Type               string `protobuf:"bytes,12,opt,name=type,proto3" json:"type,omitempty"`
	// credentials available for this provider configuration
	Credentials []*vault.Credential `protobuf:"bytes,11,rep,name=credentials,proto3" json:"credentials,omitempty"`
	Insecure    bool                `protobuf:"varint,8,opt,name=insecure,proto3" json:"insecure,omitempty"` // disable ssl/tls checks
	Sudo        *Sudo               `protobuf:"bytes,21,opt,name=sudo,proto3" json:"sudo,omitempty"`
	Record      bool                `protobuf:"varint,22,opt,name=record,proto3" json:"record,omitempty"`
	Options     map[string]string   `protobuf:"bytes,23,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// flags for additional asset discovery
	Discover *Discovery `protobuf:"bytes,27,opt,name=discover,proto3" json:"discover,omitempty"`
	// additional platform information, passed-through
	Runtime string `protobuf:"bytes,25,opt,name=runtime,proto3" json:"runtime,omitempty"`
	// configuration to uniquely identify an specific asset for multi-asset
	// connection
	PlatformId   string   `protobuf:"bytes,26,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	Capabilities []string `protobuf:"bytes,29,rep,name=capabilities,proto3" json:"capabilities,omitempty"`
	// Determines whether to delay discovery during the connection phase.
	// Discovery will only happen if Connect is called and this is false
	DelayDiscovery bool `protobuf:"varint,31,opt,name=delay_discovery,json=delayDiscovery,proto3" json:"delay_discovery,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_inventory_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{2}
}

func (x *Config) GetBackend() ProviderType {
	if x != nil {
		return x.Backend
	}
	return ProviderType_LOCAL_OS
}

func (x *Config) GetKind() DeprecatedV8_Kind {
	if x != nil {
		return x.Kind
	}
	return DeprecatedV8_Kind_KIND_UNKNOWN
}

func (x *Config) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Config) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Config) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Config) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Config) GetParentConnectionId() uint32 {
	if x != nil {
		return x.ParentConnectionId
	}
	return 0
}

func (x *Config) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Config) GetCredentials() []*vault.Credential {
	if x != nil {
		return x.Credentials
	}
	return nil
}

func (x *Config) GetInsecure() bool {
	if x != nil {
		return x.Insecure
	}
	return false
}

func (x *Config) GetSudo() *Sudo {
	if x != nil {
		return x.Sudo
	}
	return nil
}

func (x *Config) GetRecord() bool {
	if x != nil {
		return x.Record
	}
	return false
}

func (x *Config) GetOptions() map[string]string {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Config) GetDiscover() *Discovery {
	if x != nil {
		return x.Discover
	}
	return nil
}

func (x *Config) GetRuntime() string {
	if x != nil {
		return x.Runtime
	}
	return ""
}

func (x *Config) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *Config) GetCapabilities() []string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

func (x *Config) GetDelayDiscovery() bool {
	if x != nil {
		return x.DelayDiscovery
	}
	return false
}

type Sudo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Active        bool                   `protobuf:"varint,1,opt,name=active,proto3" json:"active,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Shell         string                 `protobuf:"bytes,3,opt,name=shell,proto3" json:"shell,omitempty"`
	Executable    string                 `protobuf:"bytes,4,opt,name=executable,proto3" json:"executable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sudo) Reset() {
	*x = Sudo{}
	mi := &file_inventory_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sudo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sudo) ProtoMessage() {}

func (x *Sudo) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sudo.ProtoReflect.Descriptor instead.
func (*Sudo) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{3}
}

func (x *Sudo) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *Sudo) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Sudo) GetShell() string {
	if x != nil {
		return x.Shell
	}
	return ""
}

func (x *Sudo) GetExecutable() string {
	if x != nil {
		return x.Executable
	}
	return ""
}

type Discovery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Targets       []string               `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
	Filter        map[string]string      `protobuf:"bytes,2,rep,name=filter,proto3" json:"filter,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Discovery) Reset() {
	*x = Discovery{}
	mi := &file_inventory_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Discovery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discovery) ProtoMessage() {}

func (x *Discovery) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discovery.ProtoReflect.Descriptor instead.
func (*Discovery) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{4}
}

func (x *Discovery) GetTargets() []string {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *Discovery) GetFilter() map[string]string {
	if x != nil {
		return x.Filter
	}
	return nil
}

type Platform struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Arch  string                 `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	Title string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	Family  []string `protobuf:"bytes,5,rep,name=family,proto3" json:"family,omitempty"`
	Build   string   `protobuf:"bytes,6,opt,name=build,proto3" json:"build,omitempty"`
	Version string   `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	Kind    string   `protobuf:"bytes,8,opt,name=kind,proto3" json:"kind,omitempty"`
	// technology url for this asset, raw version of an AssetUrl
	TechnologyUrlSegments []string `protobuf:"bytes,9,rep,name=technology_url_segments,json=technologyUrlSegments,proto3" json:"technology_url_segments,omitempty"`
	// FIXME: DEPRECATED, remove in v10 vv
	DeprecatedV8Kind DeprecatedV8_Kind `protobuf:"varint,20,opt,name=deprecated_v8_kind,json=deprecatedV8Kind,proto3,enum=cnquery.providers.v1.DeprecatedV8_Kind" json:"deprecated_v8_kind,omitempty"` // ^^
	Runtime          string            `protobuf:"bytes,21,opt,name=runtime,proto3" json:"runtime,omitempty"`
	// FIXME: DEPRECATED, mark as reserved in v12 (or later) vv
	Labels map[string]string `protobuf:"bytes,22,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // ^^
	// Additional platform specific/operating system data
	Metadata      map[string]string `protobuf:"bytes,23,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Platform) Reset() {
	*x = Platform{}
	mi := &file_inventory_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Platform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Platform) ProtoMessage() {}

func (x *Platform) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Platform.ProtoReflect.Descriptor instead.
func (*Platform) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{5}
}

func (x *Platform) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Platform) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *Platform) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Platform) GetFamily() []string {
	if x != nil {
		return x.Family
	}
	return nil
}

func (x *Platform) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

func (x *Platform) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Platform) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *Platform) GetTechnologyUrlSegments() []string {
	if x != nil {
		return x.TechnologyUrlSegments
	}
	return nil
}

func (x *Platform) GetDeprecatedV8Kind() DeprecatedV8_Kind {
	if x != nil {
		return x.DeprecatedV8Kind
	}
	return DeprecatedV8_Kind_KIND_UNKNOWN
}

func (x *Platform) GetRuntime() string {
	if x != nil {
		return x.Runtime
	}
	return ""
}

func (x *Platform) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Platform) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// TypeMeta describes an individual object in an API response or request
// with strings representing the type of the object and its API schema version.
// Structures that are versioned or persisted should inline TypeMeta.
type TypeMeta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Kind is a string value representing the REST resource this object
	// represents. Servers may infer this from the endpoint the client submits
	// requests to. Cannot be updated. In CamelCase.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture
	// /api-conventions.md#types-kinds
	// +optional
	Kind string `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	// APIVersion defines the versioned schema of this representation of an
	// object. Servers should convert recognized schemas to the latest internal
	// value, and may reject unrecognized values.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture
	// /api-conventions.md#resources
	// +optional
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ApiVersion    string `protobuf:"bytes,2,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TypeMeta) Reset() {
	*x = TypeMeta{}
	mi := &file_inventory_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TypeMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeMeta) ProtoMessage() {}

func (x *TypeMeta) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeMeta.ProtoReflect.Descriptor instead.
func (*TypeMeta) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{6}
}

func (x *TypeMeta) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *TypeMeta) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

// ObjectMeta is metadata that all persisted resources must have, which includes
//
//	all objects users must create.
type ObjectMeta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name must be unique within a namespace. Is required when creating
	// resources, although some resources may allow a client to request the
	// generation of an appropriate name automatically. Name is primarily intended
	// for creation idempotence and configuration definition.
	// Cannot be updated.
	// More info: http://kubernetes.io/docs/user-guide/identifiers#names
	// +optional
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Namespace defines the space within which each name must be unique. An empty
	// namespace is equivalent to the "default" namespace, but "default" is the
	// canonical representation. Not all objects are required to be scoped to a
	// namespace - the value of this field for those objects will be empty.
	//
	// Must be a DNS_LABEL.
	// Cannot be updated.
	// More info: http://kubernetes.io/docs/user-guide/namespaces
	// +optional
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Map of string keys and values that can be used to organize and categorize
	// (scope and select) objects. May match selectors of replication controllers
	// and services.
	// More info: http://kubernetes.io/docs/user-guide/labels
	// +optional
	Labels map[string]string `protobuf:"bytes,11,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Annotations is an unstructured key value map stored with a resource that \
	// may be set by external tools to store and retrieve arbitrary metadata. They
	// are not queryable and should be preserved when modifying objects.
	// More info: http://kubernetes.io/docs/user-guide/annotations
	// +optional
	Annotations map[string]string `protobuf:"bytes,12,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// List of objects depended by this object. If ALL objects in the list have
	// been deleted, this object will be garbage collected. If this object is
	// managed by a controller, then an entry in this list will point to this
	// controller, with the controller field set to true. There cannot be more
	// than one managing controller.
	// +optional
	// +patchMergeKey=uid
	// +patchStrategy=merge
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	OwnerReferences []*OwnerReference `protobuf:"bytes,13,rep,name=ownerReferences,proto3" json:"ownerReferences,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ObjectMeta) Reset() {
	*x = ObjectMeta{}
	mi := &file_inventory_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObjectMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectMeta) ProtoMessage() {}

func (x *ObjectMeta) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectMeta.ProtoReflect.Descriptor instead.
func (*ObjectMeta) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{7}
}

func (x *ObjectMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ObjectMeta) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ObjectMeta) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ObjectMeta) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *ObjectMeta) GetOwnerReferences() []*OwnerReference {
	if x != nil {
		return x.OwnerReferences
	}
	return nil
}

// Time is a wrapper around time.Time which supports correct
// marshaling to YAML and JSON.  Wrappers are provided for many
// of the factory methods that the time package offers.
//
// +protobuf.options.marshal=false
// +protobuf.as=Timestamp
// +protobuf.options.(gogoproto.goproto_stringer)=false
type Time struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Represents seconds of UTC time since Unix epoch
	// 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
	// 9999-12-31T23:59:59Z inclusive.
	Seconds int64 `protobuf:"varint,1,opt,name=seconds,proto3" json:"seconds,omitempty"`
	// Non-negative fractions of a second at nanosecond resolution. Negative
	// second values with fractions must still have non-negative nanos values
	// that count forward in time. Must be from 0 to 999,999,999
	// inclusive. This field may be limited in precision depending on context.
	Nanos         int32 `protobuf:"varint,2,opt,name=nanos,proto3" json:"nanos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Time) Reset() {
	*x = Time{}
	mi := &file_inventory_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Time) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Time) ProtoMessage() {}

func (x *Time) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Time.ProtoReflect.Descriptor instead.
func (*Time) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{8}
}

func (x *Time) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *Time) GetNanos() int32 {
	if x != nil {
		return x.Nanos
	}
	return 0
}

// OwnerReference contains enough information to let you identify an owning
// object. An owning object must be in the same namespace as the dependent, or
// be cluster-scoped, so there is no namespace field.
// +structType=atomic
type OwnerReference struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// API version of the referent.
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ApiVersion string `protobuf:"bytes,5,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`
	// Kind of the referent.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture
	// /api-conventions.md#types-kinds
	Kind string `protobuf:"bytes,1,opt,name=kind,proto3" json:"kind,omitempty"`
	// Name of the referent.
	// More info: http://kubernetes.io/docs/user-guide/identifiers#names
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// UID of the referent.
	// More info: http://kubernetes.io/docs/user-guide/identifiers#uids
	Uid           string `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OwnerReference) Reset() {
	*x = OwnerReference{}
	mi := &file_inventory_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OwnerReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OwnerReference) ProtoMessage() {}

func (x *OwnerReference) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OwnerReference.ProtoReflect.Descriptor instead.
func (*OwnerReference) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{9}
}

func (x *OwnerReference) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *OwnerReference) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *OwnerReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OwnerReference) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

// Inventory declares the all assets and their credentials
type Inventory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture
	// /api-conventions.md#metadata
	Metadata *ObjectMeta `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Specification of the desired behavior of the Inventory.
	Spec *InventorySpec `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	// Most recently observed status of the Inventory.
	Status        *InventoryStatus `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Inventory) Reset() {
	*x = Inventory{}
	mi := &file_inventory_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Inventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inventory) ProtoMessage() {}

func (x *Inventory) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inventory.ProtoReflect.Descriptor instead.
func (*Inventory) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{10}
}

func (x *Inventory) GetMetadata() *ObjectMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Inventory) GetSpec() *InventorySpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *Inventory) GetStatus() *InventoryStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type InventorySpec struct {
	state           protoimpl.MessageState       `protogen:"open.v1"`
	Assets          []*Asset                     `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	Credentials     map[string]*vault.Credential `protobuf:"bytes,2,rep,name=credentials,proto3" json:"credentials,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Vault           *vault.VaultConfiguration    `protobuf:"bytes,3,opt,name=vault,proto3" json:"vault,omitempty"`
	CredentialQuery string                       `protobuf:"bytes,4,opt,name=credential_query,json=credentialQuery,proto3" json:"credential_query,omitempty"`
	// optional: the upstream credentials to use for the inventory
	UpstreamCredentials *upstream.ServiceAccountCredentials `protobuf:"bytes,16,opt,name=upstream_credentials,json=upstreamCredentials,proto3" json:"upstream_credentials,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *InventorySpec) Reset() {
	*x = InventorySpec{}
	mi := &file_inventory_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventorySpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventorySpec) ProtoMessage() {}

func (x *InventorySpec) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventorySpec.ProtoReflect.Descriptor instead.
func (*InventorySpec) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{11}
}

func (x *InventorySpec) GetAssets() []*Asset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *InventorySpec) GetCredentials() map[string]*vault.Credential {
	if x != nil {
		return x.Credentials
	}
	return nil
}

func (x *InventorySpec) GetVault() *vault.VaultConfiguration {
	if x != nil {
		return x.Vault
	}
	return nil
}

func (x *InventorySpec) GetCredentialQuery() string {
	if x != nil {
		return x.CredentialQuery
	}
	return ""
}

func (x *InventorySpec) GetUpstreamCredentials() *upstream.ServiceAccountCredentials {
	if x != nil {
		return x.UpstreamCredentials
	}
	return nil
}

type InventoryStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InventoryStatus) Reset() {
	*x = InventoryStatus{}
	mi := &file_inventory_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InventoryStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InventoryStatus) ProtoMessage() {}

func (x *InventoryStatus) ProtoReflect() protoreflect.Message {
	mi := &file_inventory_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InventoryStatus.ProtoReflect.Descriptor instead.
func (*InventoryStatus) Descriptor() ([]byte, []int) {
	return file_inventory_proto_rawDescGZIP(), []int{12}
}

var File_inventory_proto protoreflect.FileDescriptor

const file_inventory_proto_rawDesc = "" +
	"\n" +
	"\x0finventory.proto\x12\x14cnquery.providers.v1\x1a(providers-sdk/v1/upstream/upstream.proto\x1a\"providers-sdk/v1/vault/vault.proto\"\xc8\a\n" +
	"\x05Asset\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03mrn\x18\x02 \x01(\tR\x03mrn\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12!\n" +
	"\fplatform_ids\x18\x04 \x03(\tR\vplatformIds\x121\n" +
	"\x05state\x18\x05 \x01(\x0e2\x1b.cnquery.providers.v1.StateR\x05state\x12:\n" +
	"\bplatform\x18\x06 \x01(\v2\x1e.cnquery.providers.v1.PlatformR\bplatform\x12>\n" +
	"\vconnections\x18\x11 \x03(\v2\x1c.cnquery.providers.v1.ConfigR\vconnections\x12?\n" +
	"\x06labels\x18\x12 \x03(\v2'.cnquery.providers.v1.Asset.LabelsEntryR\x06labels\x12N\n" +
	"\vannotations\x18\x13 \x03(\v2,.cnquery.providers.v1.Asset.AnnotationsEntryR\vannotations\x12B\n" +
	"\aoptions\x18\x14 \x03(\v2(.cnquery.providers.v1.Asset.OptionsEntryR\aoptions\x12\x1f\n" +
	"\vid_detector\x18\x1f \x03(\tR\n" +
	"idDetector\x12?\n" +
	"\bcategory\x18  \x01(\x0e2#.cnquery.providers.v1.AssetCategoryR\bcategory\x12B\n" +
	"\x0erelated_assets\x18! \x03(\v2\x1b.cnquery.providers.v1.AssetR\rrelatedAssets\x12\x1d\n" +
	"\n" +
	"managed_by\x18\" \x01(\tR\tmanagedBy\x12\x10\n" +
	"\x03url\x18# \x01(\tR\x03url\x12\x1f\n" +
	"\vkind_string\x18$ \x01(\tR\n" +
	"kindString\x12\x12\n" +
	"\x04fqdn\x18% \x01(\tR\x04fqdn\x12\x19\n" +
	"\btrace_id\x18& \x01(\tR\atraceId\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a>\n" +
	"\x10AnnotationsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a:\n" +
	"\fOptionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01J\x04\b\x1e\x10\x1f\"\x9f\x03\n" +
	"\x0eAssetUrlBranch\x12#\n" +
	"\rpath_segments\x18\x01 \x03(\tR\fpathSegments\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12H\n" +
	"\x06values\x18\x03 \x03(\v20.cnquery.providers.v1.AssetUrlBranch.ValuesEntryR\x06values\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x1e\n" +
	"\n" +
	"references\x18\x05 \x03(\tR\n" +
	"references\x12\x14\n" +
	"\x05depth\x18\x14 \x01(\rR\x05depth\x12<\n" +
	"\x06parent\x18\x15 \x01(\v2$.cnquery.providers.v1.AssetUrlBranchR\x06parent\x12!\n" +
	"\fparent_value\x18\x16 \x01(\tR\vparentValue\x1a_\n" +
	"\vValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12:\n" +
	"\x05value\x18\x02 \x01(\v2$.cnquery.providers.v1.AssetUrlBranchR\x05value:\x028\x01\"\xa1\x06\n" +
	"\x06Config\x12<\n" +
	"\abackend\x18\x1c \x01(\x0e2\".cnquery.providers.v1.ProviderTypeR\abackend\x12;\n" +
	"\x04kind\x18\x18 \x01(\x0e2'.cnquery.providers.v1.DeprecatedV8_KindR\x04kind\x12\x12\n" +
	"\x04host\x18\x02 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x03 \x01(\x05R\x04port\x12\x12\n" +
	"\x04path\x18\x04 \x01(\tR\x04path\x12\x0e\n" +
	"\x02id\x18\x05 \x01(\rR\x02id\x120\n" +
	"\x14parent_connection_id\x18\x1e \x01(\rR\x12parentConnectionId\x12\x12\n" +
	"\x04type\x18\f \x01(\tR\x04type\x12B\n" +
	"\vcredentials\x18\v \x03(\v2 .cnquery.providers.v1.CredentialR\vcredentials\x12\x1a\n" +
	"\binsecure\x18\b \x01(\bR\binsecure\x12.\n" +
	"\x04sudo\x18\x15 \x01(\v2\x1a.cnquery.providers.v1.SudoR\x04sudo\x12\x16\n" +
	"\x06record\x18\x16 \x01(\bR\x06record\x12C\n" +
	"\aoptions\x18\x17 \x03(\v2).cnquery.providers.v1.Config.OptionsEntryR\aoptions\x12;\n" +
	"\bdiscover\x18\x1b \x01(\v2\x1f.cnquery.providers.v1.DiscoveryR\bdiscover\x12\x18\n" +
	"\aruntime\x18\x19 \x01(\tR\aruntime\x12\x1f\n" +
	"\vplatform_id\x18\x1a \x01(\tR\n" +
	"platformId\x12\"\n" +
	"\fcapabilities\x18\x1d \x03(\tR\fcapabilities\x12'\n" +
	"\x0fdelay_discovery\x18\x1f \x01(\bR\x0edelayDiscovery\x1a:\n" +
	"\fOptionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01J\x04\b\x06\x10\aJ\x04\b\a\x10\bJ\x04\b\t\x10\n" +
	"J\x04\b\n" +
	"\x10\vJ\x04\b\x14\x10\x15\"h\n" +
	"\x04Sudo\x12\x16\n" +
	"\x06active\x18\x01 \x01(\bR\x06active\x12\x12\n" +
	"\x04user\x18\x02 \x01(\tR\x04user\x12\x14\n" +
	"\x05shell\x18\x03 \x01(\tR\x05shell\x12\x1e\n" +
	"\n" +
	"executable\x18\x04 \x01(\tR\n" +
	"executable\"\xa5\x01\n" +
	"\tDiscovery\x12\x18\n" +
	"\atargets\x18\x01 \x03(\tR\atargets\x12C\n" +
	"\x06filter\x18\x02 \x03(\v2+.cnquery.providers.v1.Discovery.FilterEntryR\x06filter\x1a9\n" +
	"\vFilterEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd3\x04\n" +
	"\bPlatform\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04arch\x18\x03 \x01(\tR\x04arch\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x16\n" +
	"\x06family\x18\x05 \x03(\tR\x06family\x12\x14\n" +
	"\x05build\x18\x06 \x01(\tR\x05build\x12\x18\n" +
	"\aversion\x18\a \x01(\tR\aversion\x12\x12\n" +
	"\x04kind\x18\b \x01(\tR\x04kind\x126\n" +
	"\x17technology_url_segments\x18\t \x03(\tR\x15technologyUrlSegments\x12U\n" +
	"\x12deprecated_v8_kind\x18\x14 \x01(\x0e2'.cnquery.providers.v1.DeprecatedV8_KindR\x10deprecatedV8Kind\x12\x18\n" +
	"\aruntime\x18\x15 \x01(\tR\aruntime\x12B\n" +
	"\x06labels\x18\x16 \x03(\v2*.cnquery.providers.v1.Platform.LabelsEntryR\x06labels\x12H\n" +
	"\bmetadata\x18\x17 \x03(\v2,.cnquery.providers.v1.Platform.MetadataEntryR\bmetadata\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\">\n" +
	"\bTypeMeta\x12\x12\n" +
	"\x04kind\x18\x01 \x01(\tR\x04kind\x12\x1e\n" +
	"\n" +
	"apiVersion\x18\x02 \x01(\tR\n" +
	"apiVersion\"\xa4\x03\n" +
	"\n" +
	"ObjectMeta\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1c\n" +
	"\tnamespace\x18\x03 \x01(\tR\tnamespace\x12D\n" +
	"\x06labels\x18\v \x03(\v2,.cnquery.providers.v1.ObjectMeta.LabelsEntryR\x06labels\x12S\n" +
	"\vannotations\x18\f \x03(\v21.cnquery.providers.v1.ObjectMeta.AnnotationsEntryR\vannotations\x12N\n" +
	"\x0fownerReferences\x18\r \x03(\v2$.cnquery.providers.v1.OwnerReferenceR\x0fownerReferences\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a>\n" +
	"\x10AnnotationsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"6\n" +
	"\x04Time\x12\x18\n" +
	"\aseconds\x18\x01 \x01(\x03R\aseconds\x12\x14\n" +
	"\x05nanos\x18\x02 \x01(\x05R\x05nanos\"j\n" +
	"\x0eOwnerReference\x12\x1e\n" +
	"\n" +
	"apiVersion\x18\x05 \x01(\tR\n" +
	"apiVersion\x12\x12\n" +
	"\x04kind\x18\x01 \x01(\tR\x04kind\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x10\n" +
	"\x03uid\x18\x04 \x01(\tR\x03uid\"\xc1\x01\n" +
	"\tInventory\x12<\n" +
	"\bmetadata\x18\x01 \x01(\v2 .cnquery.providers.v1.ObjectMetaR\bmetadata\x127\n" +
	"\x04spec\x18\x02 \x01(\v2#.cnquery.providers.v1.InventorySpecR\x04spec\x12=\n" +
	"\x06status\x18\x03 \x01(\v2%.cnquery.providers.v1.InventoryStatusR\x06status\"\xd3\x03\n" +
	"\rInventorySpec\x123\n" +
	"\x06assets\x18\x01 \x03(\v2\x1b.cnquery.providers.v1.AssetR\x06assets\x12V\n" +
	"\vcredentials\x18\x02 \x03(\v24.cnquery.providers.v1.InventorySpec.CredentialsEntryR\vcredentials\x12>\n" +
	"\x05vault\x18\x03 \x01(\v2(.cnquery.providers.v1.VaultConfigurationR\x05vault\x12)\n" +
	"\x10credential_query\x18\x04 \x01(\tR\x0fcredentialQuery\x12h\n" +
	"\x14upstream_credentials\x18\x10 \x01(\v25.mondoo.cnquery.upstream.v1.ServiceAccountCredentialsR\x13upstreamCredentials\x1a`\n" +
	"\x10CredentialsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x126\n" +
	"\x05value\x18\x02 \x01(\v2 .cnquery.providers.v1.CredentialR\x05value:\x028\x01\"\x11\n" +
	"\x0fInventoryStatus*\xec\x01\n" +
	"\x05State\x12\x11\n" +
	"\rSTATE_UNKNOWN\x10\x00\x12\x0f\n" +
	"\vSTATE_ERROR\x10\x01\x12\x11\n" +
	"\rSTATE_PENDING\x10\x02\x12\x11\n" +
	"\rSTATE_RUNNING\x10\x03\x12\x12\n" +
	"\x0eSTATE_STOPPING\x10\x04\x12\x11\n" +
	"\rSTATE_STOPPED\x10\x05\x12\x12\n" +
	"\x0eSTATE_SHUTDOWN\x10\x06\x12\x14\n" +
	"\x10STATE_TERMINATED\x10\a\x12\x10\n" +
	"\fSTATE_REBOOT\x10\b\x12\x10\n" +
	"\fSTATE_ONLINE\x10\t\x12\x11\n" +
	"\rSTATE_OFFLINE\x10\n" +
	"\x12\x11\n" +
	"\rSTATE_DELETED\x10\v*:\n" +
	"\rAssetCategory\x12\x16\n" +
	"\x12CATEGORY_INVENTORY\x10\x00\x12\x11\n" +
	"\rCATEGORY_CICD\x10\x01*\x85\x04\n" +
	"\fProviderType\x12\f\n" +
	"\bLOCAL_OS\x10\x00\x12\x17\n" +
	"\x13DOCKER_ENGINE_IMAGE\x10\x01\x12\x1b\n" +
	"\x17DOCKER_ENGINE_CONTAINER\x10\x02\x12\a\n" +
	"\x03SSH\x10\x03\x12\t\n" +
	"\x05WINRM\x10\x04\x12\x17\n" +
	"\x13AWS_SSM_RUN_COMMAND\x10\x05\x12\x16\n" +
	"\x12CONTAINER_REGISTRY\x10\x06\x12\a\n" +
	"\x03TAR\x10\a\x12\b\n" +
	"\x04MOCK\x10\b\x12\v\n" +
	"\aVSPHERE\x10\t\x12\r\n" +
	"\tARISTAEOS\x10\n" +
	"\x12\a\n" +
	"\x03AWS\x10\f\x12\a\n" +
	"\x03GCP\x10\r\x12\t\n" +
	"\x05AZURE\x10\x0e\x12\t\n" +
	"\x05MS365\x10\x0f\x12\b\n" +
	"\x04IPMI\x10\x10\x12\x0e\n" +
	"\n" +
	"VSPHERE_VM\x10\x11\x12\x06\n" +
	"\x02FS\x10\x12\x12\a\n" +
	"\x03K8S\x10\x13\x12\x11\n" +
	"\rEQUINIX_METAL\x10\x14\x12\n" +
	"\n" +
	"\x06DOCKER\x10\x15\x12\n" +
	"\n" +
	"\x06GITHUB\x10\x16\x12\v\n" +
	"\aVAGRANT\x10\x17\x12\x0f\n" +
	"\vAWS_EC2_EBS\x10\x18\x12\n" +
	"\n" +
	"\x06GITLAB\x10\x19\x12\r\n" +
	"\tTERRAFORM\x10\x1a\x12\b\n" +
	"\x04HOST\x10\x1b\x12\v\n" +
	"\aUNKNOWN\x10\x1c\x12\b\n" +
	"\x04OKTA\x10\x1d\x12\x14\n" +
	"\x10GOOGLE_WORKSPACE\x10\x1e\x12\t\n" +
	"\x05SLACK\x10\x1f\x12\a\n" +
	"\x03VCD\x10 \x12\a\n" +
	"\x03OCI\x10!\x12\t\n" +
	"\x05OPCUA\x10\"\x12!\n" +
	"\x1dGCP_COMPUTE_INSTANCE_SNAPSHOT\x10#\"\x04\b\v\x10\v*\xcb\x02\n" +
	"\x11DeprecatedV8_Kind\x12\x10\n" +
	"\fKIND_UNKNOWN\x10\x00\x12\x1e\n" +
	"\x1aKIND_VIRTUAL_MACHINE_IMAGE\x10\x01\x12\x18\n" +
	"\x14KIND_CONTAINER_IMAGE\x10\x02\x12\r\n" +
	"\tKIND_CODE\x10\x03\x12\x10\n" +
	"\fKIND_PACKAGE\x10\x04\x12\x18\n" +
	"\x14KIND_VIRTUAL_MACHINE\x10\x05\x12\x12\n" +
	"\x0eKIND_CONTAINER\x10\x06\x12\x10\n" +
	"\fKIND_PROCESS\x10\a\x12\f\n" +
	"\bKIND_API\x10\b\x12\x13\n" +
	"\x0fKIND_BARE_METAL\x10\t\x12\x10\n" +
	"\fKIND_NETWORK\x10\n" +
	"\x12\x13\n" +
	"\x0fKIND_K8S_OBJECT\x10\v\x12\x13\n" +
	"\x0fKIND_AWS_OBJECT\x10\f\x12\x13\n" +
	"\x0fKIND_GCP_OBJECT\x10\r\x12\x15\n" +
	"\x11KIND_AZURE_OBJECT\x10\x0eB6Z4go.mondoo.com/cnquery/v11/providers-sdk/v1/inventoryb\x06proto3"

var (
	file_inventory_proto_rawDescOnce sync.Once
	file_inventory_proto_rawDescData []byte
)

func file_inventory_proto_rawDescGZIP() []byte {
	file_inventory_proto_rawDescOnce.Do(func() {
		file_inventory_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_inventory_proto_rawDesc), len(file_inventory_proto_rawDesc)))
	})
	return file_inventory_proto_rawDescData
}

var file_inventory_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_inventory_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_inventory_proto_goTypes = []any{
	(State)(0),                       // 0: cnquery.providers.v1.State
	(AssetCategory)(0),               // 1: cnquery.providers.v1.AssetCategory
	(ProviderType)(0),                // 2: cnquery.providers.v1.ProviderType
	(DeprecatedV8_Kind)(0),           // 3: cnquery.providers.v1.DeprecatedV8_Kind
	(*Asset)(nil),                    // 4: cnquery.providers.v1.Asset
	(*AssetUrlBranch)(nil),           // 5: cnquery.providers.v1.AssetUrlBranch
	(*Config)(nil),                   // 6: cnquery.providers.v1.Config
	(*Sudo)(nil),                     // 7: cnquery.providers.v1.Sudo
	(*Discovery)(nil),                // 8: cnquery.providers.v1.Discovery
	(*Platform)(nil),                 // 9: cnquery.providers.v1.Platform
	(*TypeMeta)(nil),                 // 10: cnquery.providers.v1.TypeMeta
	(*ObjectMeta)(nil),               // 11: cnquery.providers.v1.ObjectMeta
	(*Time)(nil),                     // 12: cnquery.providers.v1.Time
	(*OwnerReference)(nil),           // 13: cnquery.providers.v1.OwnerReference
	(*Inventory)(nil),                // 14: cnquery.providers.v1.Inventory
	(*InventorySpec)(nil),            // 15: cnquery.providers.v1.InventorySpec
	(*InventoryStatus)(nil),          // 16: cnquery.providers.v1.InventoryStatus
	nil,                              // 17: cnquery.providers.v1.Asset.LabelsEntry
	nil,                              // 18: cnquery.providers.v1.Asset.AnnotationsEntry
	nil,                              // 19: cnquery.providers.v1.Asset.OptionsEntry
	nil,                              // 20: cnquery.providers.v1.AssetUrlBranch.ValuesEntry
	nil,                              // 21: cnquery.providers.v1.Config.OptionsEntry
	nil,                              // 22: cnquery.providers.v1.Discovery.FilterEntry
	nil,                              // 23: cnquery.providers.v1.Platform.LabelsEntry
	nil,                              // 24: cnquery.providers.v1.Platform.MetadataEntry
	nil,                              // 25: cnquery.providers.v1.ObjectMeta.LabelsEntry
	nil,                              // 26: cnquery.providers.v1.ObjectMeta.AnnotationsEntry
	nil,                              // 27: cnquery.providers.v1.InventorySpec.CredentialsEntry
	(*vault.Credential)(nil),         // 28: cnquery.providers.v1.Credential
	(*vault.VaultConfiguration)(nil), // 29: cnquery.providers.v1.VaultConfiguration
	(*upstream.ServiceAccountCredentials)(nil), // 30: mondoo.cnquery.upstream.v1.ServiceAccountCredentials
}
var file_inventory_proto_depIdxs = []int32{
	0,  // 0: cnquery.providers.v1.Asset.state:type_name -> cnquery.providers.v1.State
	9,  // 1: cnquery.providers.v1.Asset.platform:type_name -> cnquery.providers.v1.Platform
	6,  // 2: cnquery.providers.v1.Asset.connections:type_name -> cnquery.providers.v1.Config
	17, // 3: cnquery.providers.v1.Asset.labels:type_name -> cnquery.providers.v1.Asset.LabelsEntry
	18, // 4: cnquery.providers.v1.Asset.annotations:type_name -> cnquery.providers.v1.Asset.AnnotationsEntry
	19, // 5: cnquery.providers.v1.Asset.options:type_name -> cnquery.providers.v1.Asset.OptionsEntry
	1,  // 6: cnquery.providers.v1.Asset.category:type_name -> cnquery.providers.v1.AssetCategory
	4,  // 7: cnquery.providers.v1.Asset.related_assets:type_name -> cnquery.providers.v1.Asset
	20, // 8: cnquery.providers.v1.AssetUrlBranch.values:type_name -> cnquery.providers.v1.AssetUrlBranch.ValuesEntry
	5,  // 9: cnquery.providers.v1.AssetUrlBranch.parent:type_name -> cnquery.providers.v1.AssetUrlBranch
	2,  // 10: cnquery.providers.v1.Config.backend:type_name -> cnquery.providers.v1.ProviderType
	3,  // 11: cnquery.providers.v1.Config.kind:type_name -> cnquery.providers.v1.DeprecatedV8_Kind
	28, // 12: cnquery.providers.v1.Config.credentials:type_name -> cnquery.providers.v1.Credential
	7,  // 13: cnquery.providers.v1.Config.sudo:type_name -> cnquery.providers.v1.Sudo
	21, // 14: cnquery.providers.v1.Config.options:type_name -> cnquery.providers.v1.Config.OptionsEntry
	8,  // 15: cnquery.providers.v1.Config.discover:type_name -> cnquery.providers.v1.Discovery
	22, // 16: cnquery.providers.v1.Discovery.filter:type_name -> cnquery.providers.v1.Discovery.FilterEntry
	3,  // 17: cnquery.providers.v1.Platform.deprecated_v8_kind:type_name -> cnquery.providers.v1.DeprecatedV8_Kind
	23, // 18: cnquery.providers.v1.Platform.labels:type_name -> cnquery.providers.v1.Platform.LabelsEntry
	24, // 19: cnquery.providers.v1.Platform.metadata:type_name -> cnquery.providers.v1.Platform.MetadataEntry
	25, // 20: cnquery.providers.v1.ObjectMeta.labels:type_name -> cnquery.providers.v1.ObjectMeta.LabelsEntry
	26, // 21: cnquery.providers.v1.ObjectMeta.annotations:type_name -> cnquery.providers.v1.ObjectMeta.AnnotationsEntry
	13, // 22: cnquery.providers.v1.ObjectMeta.ownerReferences:type_name -> cnquery.providers.v1.OwnerReference
	11, // 23: cnquery.providers.v1.Inventory.metadata:type_name -> cnquery.providers.v1.ObjectMeta
	15, // 24: cnquery.providers.v1.Inventory.spec:type_name -> cnquery.providers.v1.InventorySpec
	16, // 25: cnquery.providers.v1.Inventory.status:type_name -> cnquery.providers.v1.InventoryStatus
	4,  // 26: cnquery.providers.v1.InventorySpec.assets:type_name -> cnquery.providers.v1.Asset
	27, // 27: cnquery.providers.v1.InventorySpec.credentials:type_name -> cnquery.providers.v1.InventorySpec.CredentialsEntry
	29, // 28: cnquery.providers.v1.InventorySpec.vault:type_name -> cnquery.providers.v1.VaultConfiguration
	30, // 29: cnquery.providers.v1.InventorySpec.upstream_credentials:type_name -> mondoo.cnquery.upstream.v1.ServiceAccountCredentials
	5,  // 30: cnquery.providers.v1.AssetUrlBranch.ValuesEntry.value:type_name -> cnquery.providers.v1.AssetUrlBranch
	28, // 31: cnquery.providers.v1.InventorySpec.CredentialsEntry.value:type_name -> cnquery.providers.v1.Credential
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_inventory_proto_init() }
func file_inventory_proto_init() {
	if File_inventory_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_inventory_proto_rawDesc), len(file_inventory_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inventory_proto_goTypes,
		DependencyIndexes: file_inventory_proto_depIdxs,
		EnumInfos:         file_inventory_proto_enumTypes,
		MessageInfos:      file_inventory_proto_msgTypes,
	}.Build()
	File_inventory_proto = out.File
	file_inventory_proto_goTypes = nil
	file_inventory_proto_depIdxs = nil
}
