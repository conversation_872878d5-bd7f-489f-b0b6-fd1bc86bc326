// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cvss.proto

package cvss

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Cvss struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Vector        string                 `protobuf:"bytes,1,opt,name=vector,proto3" json:"vector,omitempty"`
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Score         float32                `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cvss) Reset() {
	*x = Cvss{}
	mi := &file_cvss_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cvss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cvss) ProtoMessage() {}

func (x *Cvss) ProtoReflect() protoreflect.Message {
	mi := &file_cvss_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cvss.ProtoReflect.Descriptor instead.
func (*Cvss) Descriptor() ([]byte, []int) {
	return file_cvss_proto_rawDescGZIP(), []int{0}
}

func (x *Cvss) GetVector() string {
	if x != nil {
		return x.Vector
	}
	return ""
}

func (x *Cvss) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Cvss) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

var File_cvss_proto protoreflect.FileDescriptor

const file_cvss_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"cvss.proto\x12\x12mondoo.mvd.cvss.v1\"L\n" +
	"\x04Cvss\x12\x16\n" +
	"\x06vector\x18\x01 \x01(\tR\x06vector\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x14\n" +
	"\x05score\x18\x03 \x01(\x02R\x05scoreB>Z<go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream/mvd/cvssb\x06proto3"

var (
	file_cvss_proto_rawDescOnce sync.Once
	file_cvss_proto_rawDescData []byte
)

func file_cvss_proto_rawDescGZIP() []byte {
	file_cvss_proto_rawDescOnce.Do(func() {
		file_cvss_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cvss_proto_rawDesc), len(file_cvss_proto_rawDesc)))
	})
	return file_cvss_proto_rawDescData
}

var file_cvss_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_cvss_proto_goTypes = []any{
	(*Cvss)(nil), // 0: mondoo.mvd.cvss.v1.Cvss
}
var file_cvss_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_cvss_proto_init() }
func file_cvss_proto_init() {
	if File_cvss_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cvss_proto_rawDesc), len(file_cvss_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cvss_proto_goTypes,
		DependencyIndexes: file_cvss_proto_depIdxs,
		MessageInfos:      file_cvss_proto_msgTypes,
	}.Build()
	File_cvss_proto = out.File
	file_cvss_proto_goTypes = nil
	file_cvss_proto_depIdxs = nil
}
