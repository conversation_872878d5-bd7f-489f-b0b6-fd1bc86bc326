// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: mvd.proto

package mvd

import (
	cvss "go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream/mvd/cvss"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AdvisoryState int32

const (
	AdvisoryState_RELEASED     AdvisoryState = 0 // protolint:disable ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	AdvisoryState_PENDING      AdvisoryState = 1
	AdvisoryState_IGNORED      AdvisoryState = 2
	AdvisoryState_NOT_AFFECTED AdvisoryState = 3
)

// Enum value maps for AdvisoryState.
var (
	AdvisoryState_name = map[int32]string{
		0: "RELEASED",
		1: "PENDING",
		2: "IGNORED",
		3: "NOT_AFFECTED",
	}
	AdvisoryState_value = map[string]int32{
		"RELEASED":     0,
		"PENDING":      1,
		"IGNORED":      2,
		"NOT_AFFECTED": 3,
	}
)

func (x AdvisoryState) Enum() *AdvisoryState {
	p := new(AdvisoryState)
	*p = x
	return p
}

func (x AdvisoryState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdvisoryState) Descriptor() protoreflect.EnumDescriptor {
	return file_mvd_proto_enumTypes[0].Descriptor()
}

func (AdvisoryState) Type() protoreflect.EnumType {
	return &file_mvd_proto_enumTypes[0]
}

func (x AdvisoryState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdvisoryState.Descriptor instead.
func (AdvisoryState) EnumDescriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{0}
}

// protolint:disable:next ENUM_FIELD_NAMES_PREFIX
type CveState int32

const (
	// protolint:disable:next ENUM_FIELD_NAMES_ZERO_VALUE_END_WITH
	CveState_PUBLIC      CveState = 0
	CveState_INVALID     CveState = 1
	CveState_RESERVED    CveState = 2
	CveState_REPLACED_BY CveState = 4
	CveState_SPLIT_FROM  CveState = 5
	CveState_MERGED_TO   CveState = 6
	CveState_REJECTED    CveState = 7
)

// Enum value maps for CveState.
var (
	CveState_name = map[int32]string{
		0: "PUBLIC",
		1: "INVALID",
		2: "RESERVED",
		4: "REPLACED_BY",
		5: "SPLIT_FROM",
		6: "MERGED_TO",
		7: "REJECTED",
	}
	CveState_value = map[string]int32{
		"PUBLIC":      0,
		"INVALID":     1,
		"RESERVED":    2,
		"REPLACED_BY": 4,
		"SPLIT_FROM":  5,
		"MERGED_TO":   6,
		"REJECTED":    7,
	}
)

func (x CveState) Enum() *CveState {
	p := new(CveState)
	*p = x
	return p
}

func (x CveState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CveState) Descriptor() protoreflect.EnumDescriptor {
	return file_mvd_proto_enumTypes[1].Descriptor()
}

func (CveState) Type() protoreflect.EnumType {
	return &file_mvd_proto_enumTypes[1]
}

func (x CveState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CveState.Descriptor instead.
func (CveState) EnumDescriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{1}
}

type Platform struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Release       string                 `protobuf:"bytes,2,opt,name=release,proto3" json:"release,omitempty"`
	Arch          string                 `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Build         string                 `protobuf:"bytes,6,opt,name=build,proto3" json:"build,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,22,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Platform) Reset() {
	*x = Platform{}
	mi := &file_mvd_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Platform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Platform) ProtoMessage() {}

func (x *Platform) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Platform.ProtoReflect.Descriptor instead.
func (*Platform) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{0}
}

func (x *Platform) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Platform) GetRelease() string {
	if x != nil {
		return x.Release
	}
	return ""
}

func (x *Platform) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *Platform) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Platform) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

func (x *Platform) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Package struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ID            string `protobuf:"bytes,6,opt,name=ID,proto3" json:"ID,omitempty"`
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version       string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Arch          string `protobuf:"bytes,5,opt,name=arch,proto3" json:"arch,omitempty"`
	Description   string `protobuf:"bytes,20,opt,name=description,proto3" json:"description,omitempty"`
	Format        string `protobuf:"bytes,23,opt,name=format,proto3" json:"format,omitempty"`
	Origin        string `protobuf:"bytes,24,opt,name=origin,proto3" json:"origin,omitempty"`
	Available     string `protobuf:"bytes,22,opt,name=available,proto3" json:"available,omitempty"`
	Status        string `protobuf:"bytes,21,opt,name=status,proto3" json:"status,omitempty"`
	Namespace     string `protobuf:"bytes,25,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Score         int32  `protobuf:"varint,26,opt,name=score,proto3" json:"score,omitempty"`
	Affected      bool   `protobuf:"varint,27,opt,name=affected,proto3" json:"affected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Package) Reset() {
	*x = Package{}
	mi := &file_mvd_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{1}
}

func (x *Package) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Package) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Package) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Package) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *Package) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Package) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Package) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *Package) GetAvailable() string {
	if x != nil {
		return x.Available
	}
	return ""
}

func (x *Package) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Package) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Package) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Package) GetAffected() bool {
	if x != nil {
		return x.Affected
	}
	return false
}

type AnalyseAssetRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Platform *Platform              `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	Packages []*Package             `protobuf:"bytes,2,rep,name=packages,proto3" json:"packages,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	KernelVersion string `protobuf:"bytes,3,opt,name=kernelVersion,proto3" json:"kernelVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyseAssetRequest) Reset() {
	*x = AnalyseAssetRequest{}
	mi := &file_mvd_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyseAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyseAssetRequest) ProtoMessage() {}

func (x *AnalyseAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyseAssetRequest.ProtoReflect.Descriptor instead.
func (*AnalyseAssetRequest) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{2}
}

func (x *AnalyseAssetRequest) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *AnalyseAssetRequest) GetPackages() []*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *AnalyseAssetRequest) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

type VulnReport struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Platform      *Platform              `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	Packages      []*Package             `protobuf:"bytes,2,rep,name=packages,proto3" json:"packages,omitempty"`
	Advisories    []*Advisory            `protobuf:"bytes,3,rep,name=advisories,proto3" json:"advisories,omitempty"`
	Stats         *ReportStats           `protobuf:"bytes,4,opt,name=stats,proto3" json:"stats,omitempty"`
	Published     string                 `protobuf:"bytes,5,opt,name=published,proto3" json:"published,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VulnReport) Reset() {
	*x = VulnReport{}
	mi := &file_mvd_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VulnReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnReport) ProtoMessage() {}

func (x *VulnReport) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnReport.ProtoReflect.Descriptor instead.
func (*VulnReport) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{3}
}

func (x *VulnReport) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *VulnReport) GetPackages() []*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *VulnReport) GetAdvisories() []*Advisory {
	if x != nil {
		return x.Advisories
	}
	return nil
}

func (x *VulnReport) GetStats() *ReportStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *VulnReport) GetPublished() string {
	if x != nil {
		return x.Published
	}
	return ""
}

type Advisory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	Mrn         string `protobuf:"bytes,2,opt,name=Mrn,proto3" json:"Mrn,omitempty"`
	Title       string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	Fixed []*Package `protobuf:"bytes,5,rep,name=fixed,proto3" json:"fixed,omitempty"`
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	Affected []*Package   `protobuf:"bytes,6,rep,name=affected,proto3" json:"affected,omitempty"`
	Refs     []*Reference `protobuf:"bytes,7,rep,name=refs,proto3" json:"refs,omitempty"`
	Cves     []*CVE       `protobuf:"bytes,20,rep,name=cves,proto3" json:"cves,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	FixedPlatforms []*FixedPlatform `protobuf:"bytes,30,rep,name=fixedPlatforms,proto3" json:"fixedPlatforms,omitempty"`
	Score          int32            `protobuf:"varint,25,opt,name=score,proto3" json:"score,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	WorstScore  *cvss.Cvss    `protobuf:"bytes,31,opt,name=worstScore,proto3" json:"worstScore,omitempty"`
	Vendorscore float32       `protobuf:"fixed32,26,opt,name=vendorscore,proto3" json:"vendorscore,omitempty"`
	State       AdvisoryState `protobuf:"varint,27,opt,name=state,proto3,enum=mondoo.mvd.v1.AdvisoryState" json:"state,omitempty"`
	Published   string        `protobuf:"bytes,23,opt,name=published,proto3" json:"published,omitempty"`
	Modified    string        `protobuf:"bytes,24,opt,name=modified,proto3" json:"modified,omitempty"`
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	Supersedence  []string `protobuf:"bytes,28,rep,name=supersedence,proto3" json:"supersedence,omitempty"`
	Unscored      bool     `protobuf:"varint,29,opt,name=unscored,proto3" json:"unscored,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Advisory) Reset() {
	*x = Advisory{}
	mi := &file_mvd_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Advisory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Advisory) ProtoMessage() {}

func (x *Advisory) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Advisory.ProtoReflect.Descriptor instead.
func (*Advisory) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{4}
}

func (x *Advisory) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Advisory) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *Advisory) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Advisory) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Advisory) GetFixed() []*Package {
	if x != nil {
		return x.Fixed
	}
	return nil
}

func (x *Advisory) GetAffected() []*Package {
	if x != nil {
		return x.Affected
	}
	return nil
}

func (x *Advisory) GetRefs() []*Reference {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *Advisory) GetCves() []*CVE {
	if x != nil {
		return x.Cves
	}
	return nil
}

func (x *Advisory) GetFixedPlatforms() []*FixedPlatform {
	if x != nil {
		return x.FixedPlatforms
	}
	return nil
}

func (x *Advisory) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Advisory) GetWorstScore() *cvss.Cvss {
	if x != nil {
		return x.WorstScore
	}
	return nil
}

func (x *Advisory) GetVendorscore() float32 {
	if x != nil {
		return x.Vendorscore
	}
	return 0
}

func (x *Advisory) GetState() AdvisoryState {
	if x != nil {
		return x.State
	}
	return AdvisoryState_RELEASED
}

func (x *Advisory) GetPublished() string {
	if x != nil {
		return x.Published
	}
	return ""
}

func (x *Advisory) GetModified() string {
	if x != nil {
		return x.Modified
	}
	return ""
}

func (x *Advisory) GetSupersedence() []string {
	if x != nil {
		return x.Supersedence
	}
	return nil
}

func (x *Advisory) GetUnscored() bool {
	if x != nil {
		return x.Unscored
	}
	return false
}

type Reference struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ID            string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Url           string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Source        string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	Title         string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Reference) Reset() {
	*x = Reference{}
	mi := &file_mvd_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{5}
}

func (x *Reference) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Reference) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Reference) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Reference) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type FixedPlatform struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ID        string `protobuf:"bytes,6,opt,name=ID,proto3" json:"ID,omitempty"`
	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Release   string `protobuf:"bytes,2,opt,name=release,proto3" json:"release,omitempty"`
	Arch      string `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
	Namespace string `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Build     string `protobuf:"bytes,5,opt,name=build,proto3" json:"build,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	PatchName     string `protobuf:"bytes,7,opt,name=patchName,proto3" json:"patchName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FixedPlatform) Reset() {
	*x = FixedPlatform{}
	mi := &file_mvd_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FixedPlatform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixedPlatform) ProtoMessage() {}

func (x *FixedPlatform) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixedPlatform.ProtoReflect.Descriptor instead.
func (*FixedPlatform) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{6}
}

func (x *FixedPlatform) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *FixedPlatform) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FixedPlatform) GetRelease() string {
	if x != nil {
		return x.Release
	}
	return ""
}

func (x *FixedPlatform) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *FixedPlatform) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *FixedPlatform) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

func (x *FixedPlatform) GetPatchName() string {
	if x != nil {
		return x.PatchName
	}
	return ""
}

type ReportStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Score         int32                  `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	Affected      bool                   `protobuf:"varint,2,opt,name=affected,proto3" json:"affected,omitempty"`
	Unscored      bool                   `protobuf:"varint,3,opt,name=unscored,proto3" json:"unscored,omitempty"`
	Advisories    *ReportStatsAdvisories `protobuf:"bytes,16,opt,name=advisories,proto3" json:"advisories,omitempty"`
	Cves          *ReportStatsCves       `protobuf:"bytes,17,opt,name=cves,proto3" json:"cves,omitempty"`
	Packages      *ReportStatsPackages   `protobuf:"bytes,18,opt,name=packages,proto3" json:"packages,omitempty"`
	Exploits      *ReportStatsExploits   `protobuf:"bytes,19,opt,name=exploits,proto3" json:"exploits,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportStats) Reset() {
	*x = ReportStats{}
	mi := &file_mvd_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStats) ProtoMessage() {}

func (x *ReportStats) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStats.ProtoReflect.Descriptor instead.
func (*ReportStats) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{7}
}

func (x *ReportStats) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ReportStats) GetAffected() bool {
	if x != nil {
		return x.Affected
	}
	return false
}

func (x *ReportStats) GetUnscored() bool {
	if x != nil {
		return x.Unscored
	}
	return false
}

func (x *ReportStats) GetAdvisories() *ReportStatsAdvisories {
	if x != nil {
		return x.Advisories
	}
	return nil
}

func (x *ReportStats) GetCves() *ReportStatsCves {
	if x != nil {
		return x.Cves
	}
	return nil
}

func (x *ReportStats) GetPackages() *ReportStatsPackages {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *ReportStats) GetExploits() *ReportStatsExploits {
	if x != nil {
		return x.Exploits
	}
	return nil
}

type ReportStatsAdvisories struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Critical      int32                  `protobuf:"varint,2,opt,name=critical,proto3" json:"critical,omitempty"`
	High          int32                  `protobuf:"varint,3,opt,name=high,proto3" json:"high,omitempty"`
	Medium        int32                  `protobuf:"varint,4,opt,name=medium,proto3" json:"medium,omitempty"`
	Low           int32                  `protobuf:"varint,5,opt,name=low,proto3" json:"low,omitempty"`
	None          int32                  `protobuf:"varint,6,opt,name=none,proto3" json:"none,omitempty"`
	Unknown       int32                  `protobuf:"varint,7,opt,name=unknown,proto3" json:"unknown,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportStatsAdvisories) Reset() {
	*x = ReportStatsAdvisories{}
	mi := &file_mvd_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportStatsAdvisories) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStatsAdvisories) ProtoMessage() {}

func (x *ReportStatsAdvisories) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStatsAdvisories.ProtoReflect.Descriptor instead.
func (*ReportStatsAdvisories) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{8}
}

func (x *ReportStatsAdvisories) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReportStatsAdvisories) GetCritical() int32 {
	if x != nil {
		return x.Critical
	}
	return 0
}

func (x *ReportStatsAdvisories) GetHigh() int32 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *ReportStatsAdvisories) GetMedium() int32 {
	if x != nil {
		return x.Medium
	}
	return 0
}

func (x *ReportStatsAdvisories) GetLow() int32 {
	if x != nil {
		return x.Low
	}
	return 0
}

func (x *ReportStatsAdvisories) GetNone() int32 {
	if x != nil {
		return x.None
	}
	return 0
}

func (x *ReportStatsAdvisories) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type ReportStatsCves struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Critical      int32                  `protobuf:"varint,2,opt,name=critical,proto3" json:"critical,omitempty"`
	High          int32                  `protobuf:"varint,3,opt,name=high,proto3" json:"high,omitempty"`
	Medium        int32                  `protobuf:"varint,4,opt,name=medium,proto3" json:"medium,omitempty"`
	Low           int32                  `protobuf:"varint,5,opt,name=low,proto3" json:"low,omitempty"`
	None          int32                  `protobuf:"varint,6,opt,name=none,proto3" json:"none,omitempty"`
	Unknown       int32                  `protobuf:"varint,7,opt,name=unknown,proto3" json:"unknown,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportStatsCves) Reset() {
	*x = ReportStatsCves{}
	mi := &file_mvd_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportStatsCves) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStatsCves) ProtoMessage() {}

func (x *ReportStatsCves) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStatsCves.ProtoReflect.Descriptor instead.
func (*ReportStatsCves) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{9}
}

func (x *ReportStatsCves) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReportStatsCves) GetCritical() int32 {
	if x != nil {
		return x.Critical
	}
	return 0
}

func (x *ReportStatsCves) GetHigh() int32 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *ReportStatsCves) GetMedium() int32 {
	if x != nil {
		return x.Medium
	}
	return 0
}

func (x *ReportStatsCves) GetLow() int32 {
	if x != nil {
		return x.Low
	}
	return 0
}

func (x *ReportStatsCves) GetNone() int32 {
	if x != nil {
		return x.None
	}
	return 0
}

func (x *ReportStatsCves) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type ReportStatsPackages struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Affected      int32                  `protobuf:"varint,2,opt,name=affected,proto3" json:"affected,omitempty"`
	Critical      int32                  `protobuf:"varint,3,opt,name=critical,proto3" json:"critical,omitempty"`
	High          int32                  `protobuf:"varint,4,opt,name=high,proto3" json:"high,omitempty"`
	Medium        int32                  `protobuf:"varint,5,opt,name=medium,proto3" json:"medium,omitempty"`
	Low           int32                  `protobuf:"varint,6,opt,name=low,proto3" json:"low,omitempty"`
	None          int32                  `protobuf:"varint,7,opt,name=none,proto3" json:"none,omitempty"`
	Unknown       int32                  `protobuf:"varint,8,opt,name=unknown,proto3" json:"unknown,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportStatsPackages) Reset() {
	*x = ReportStatsPackages{}
	mi := &file_mvd_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportStatsPackages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStatsPackages) ProtoMessage() {}

func (x *ReportStatsPackages) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStatsPackages.ProtoReflect.Descriptor instead.
func (*ReportStatsPackages) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{10}
}

func (x *ReportStatsPackages) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ReportStatsPackages) GetAffected() int32 {
	if x != nil {
		return x.Affected
	}
	return 0
}

func (x *ReportStatsPackages) GetCritical() int32 {
	if x != nil {
		return x.Critical
	}
	return 0
}

func (x *ReportStatsPackages) GetHigh() int32 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *ReportStatsPackages) GetMedium() int32 {
	if x != nil {
		return x.Medium
	}
	return 0
}

func (x *ReportStatsPackages) GetLow() int32 {
	if x != nil {
		return x.Low
	}
	return 0
}

func (x *ReportStatsPackages) GetNone() int32 {
	if x != nil {
		return x.None
	}
	return 0
}

func (x *ReportStatsPackages) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type ReportStatsExploits struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportStatsExploits) Reset() {
	*x = ReportStatsExploits{}
	mi := &file_mvd_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportStatsExploits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStatsExploits) ProtoMessage() {}

func (x *ReportStatsExploits) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStatsExploits.ProtoReflect.Descriptor instead.
func (*ReportStatsExploits) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{11}
}

func (x *ReportStatsExploits) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type CVE struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	Mrn     string  `protobuf:"bytes,2,opt,name=Mrn,proto3" json:"Mrn,omitempty"`
	Summary string  `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary,omitempty"`
	Score   float32 `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	WorstScore *cvss.Cvss `protobuf:"bytes,7,opt,name=worstScore,proto3" json:"worstScore,omitempty"`
	Unscored   bool       `protobuf:"varint,6,opt,name=unscored,proto3" json:"unscored,omitempty"`
	State      CveState   `protobuf:"varint,5,opt,name=state,proto3,enum=mondoo.mvd.v1.CveState" json:"state,omitempty"`
	// protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
	Cvss          []*cvss.Cvss `protobuf:"bytes,20,rep,name=cvss,proto3" json:"cvss,omitempty"`
	Cwe           string       `protobuf:"bytes,21,opt,name=cwe,proto3" json:"cwe,omitempty"`
	Published     string       `protobuf:"bytes,22,opt,name=published,proto3" json:"published,omitempty"`
	Modified      string       `protobuf:"bytes,23,opt,name=modified,proto3" json:"modified,omitempty"`
	Url           string       `protobuf:"bytes,24,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CVE) Reset() {
	*x = CVE{}
	mi := &file_mvd_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CVE) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CVE) ProtoMessage() {}

func (x *CVE) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CVE.ProtoReflect.Descriptor instead.
func (*CVE) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{12}
}

func (x *CVE) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *CVE) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *CVE) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *CVE) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *CVE) GetWorstScore() *cvss.Cvss {
	if x != nil {
		return x.WorstScore
	}
	return nil
}

func (x *CVE) GetUnscored() bool {
	if x != nil {
		return x.Unscored
	}
	return false
}

func (x *CVE) GetState() CveState {
	if x != nil {
		return x.State
	}
	return CveState_PUBLIC
}

func (x *CVE) GetCvss() []*cvss.Cvss {
	if x != nil {
		return x.Cvss
	}
	return nil
}

func (x *CVE) GetCwe() string {
	if x != nil {
		return x.Cwe
	}
	return ""
}

func (x *CVE) GetPublished() string {
	if x != nil {
		return x.Published
	}
	return ""
}

func (x *CVE) GetModified() string {
	if x != nil {
		return x.Modified
	}
	return ""
}

func (x *CVE) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type PlatformEolInfo struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Platform *Platform              `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	DocsUrl string `protobuf:"bytes,2,opt,name=DocsUrl,proto3" json:"DocsUrl,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ProductUrl string `protobuf:"bytes,3,opt,name=ProductUrl,proto3" json:"ProductUrl,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	ReleaseDate string `protobuf:"bytes,4,opt,name=ReleaseDate,proto3" json:"ReleaseDate,omitempty"`
	// protolint:disable:next FIELD_NAMES_LOWER_SNAKE_CASE
	EolDate       string `protobuf:"bytes,5,opt,name=EolDate,proto3" json:"EolDate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformEolInfo) Reset() {
	*x = PlatformEolInfo{}
	mi := &file_mvd_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformEolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformEolInfo) ProtoMessage() {}

func (x *PlatformEolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformEolInfo.ProtoReflect.Descriptor instead.
func (*PlatformEolInfo) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{13}
}

func (x *PlatformEolInfo) GetPlatform() *Platform {
	if x != nil {
		return x.Platform
	}
	return nil
}

func (x *PlatformEolInfo) GetDocsUrl() string {
	if x != nil {
		return x.DocsUrl
	}
	return ""
}

func (x *PlatformEolInfo) GetProductUrl() string {
	if x != nil {
		return x.ProductUrl
	}
	return ""
}

func (x *PlatformEolInfo) GetReleaseDate() string {
	if x != nil {
		return x.ReleaseDate
	}
	return ""
}

func (x *PlatformEolInfo) GetEolDate() string {
	if x != nil {
		return x.EolDate
	}
	return ""
}

// GetProductEolRequest contains information about the product and the version
type GetProductEolRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Product ID eg. kubernetes
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Product version
	Version       string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProductEolRequest) Reset() {
	*x = GetProductEolRequest{}
	mi := &file_mvd_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductEolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductEolRequest) ProtoMessage() {}

func (x *GetProductEolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductEolRequest.ProtoReflect.Descriptor instead.
func (*GetProductEolRequest) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{14}
}

func (x *GetProductEolRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetProductEolRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// GetProductEolResponse contains information about the end of life of a product
// release
type GetProductEolResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Release information
	Release       *EndOfLifeRelease `protobuf:"bytes,1,opt,name=release,proto3" json:"release,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProductEolResponse) Reset() {
	*x = GetProductEolResponse{}
	mi := &file_mvd_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductEolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductEolResponse) ProtoMessage() {}

func (x *GetProductEolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductEolResponse.ProtoReflect.Descriptor instead.
func (*GetProductEolResponse) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{15}
}

func (x *GetProductEolResponse) GetRelease() *EndOfLifeRelease {
	if x != nil {
		return x.Release
	}
	return nil
}

// EndOfLifeRelease contains information about the end of life of a release
type EndOfLifeRelease struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Release Name eg. Ubuntu
	ReleaseName string `protobuf:"bytes,1,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	// Release Codename eg. Focal Fossa
	ReleaseCodename string `protobuf:"bytes,2,opt,name=release_codename,json=releaseCodename,proto3" json:"release_codename,omitempty"`
	// Release Cycle eg. major version
	ReleaseCycle string `protobuf:"bytes,3,opt,name=release_cycle,json=releaseCycle,proto3" json:"release_cycle,omitempty"`
	// Latest version in the release cycle
	LatestVersion string `protobuf:"bytes,4,opt,name=latest_version,json=latestVersion,proto3" json:"latest_version,omitempty"`
	// First Release Date
	FirstReleaseDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=first_release_date,json=firstReleaseDate,proto3" json:"first_release_date,omitempty"`
	// Last Release Date
	LastReleaseDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_release_date,json=lastReleaseDate,proto3" json:"last_release_date,omitempty"`
	// Link to the release
	ReleaseLink string `protobuf:"bytes,7,opt,name=release_link,json=releaseLink,proto3" json:"release_link,omitempty"`
	// End Of Active Support
	EndOfActiveSupport *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=end_of_active_support,json=endOfActiveSupport,proto3" json:"end_of_active_support,omitempty"`
	// End Of Life
	EndOfLife *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=end_of_life,json=endOfLife,proto3" json:"end_of_life,omitempty"`
	// End Of Extended Support
	EndOfExtendedSupport *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=end_of_extended_support,json=endOfExtendedSupport,proto3" json:"end_of_extended_support,omitempty"`
	// Change Log Reference
	ChangeLogReference string `protobuf:"bytes,11,opt,name=change_log_reference,json=changeLogReference,proto3" json:"change_log_reference,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *EndOfLifeRelease) Reset() {
	*x = EndOfLifeRelease{}
	mi := &file_mvd_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndOfLifeRelease) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndOfLifeRelease) ProtoMessage() {}

func (x *EndOfLifeRelease) ProtoReflect() protoreflect.Message {
	mi := &file_mvd_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndOfLifeRelease.ProtoReflect.Descriptor instead.
func (*EndOfLifeRelease) Descriptor() ([]byte, []int) {
	return file_mvd_proto_rawDescGZIP(), []int{16}
}

func (x *EndOfLifeRelease) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *EndOfLifeRelease) GetReleaseCodename() string {
	if x != nil {
		return x.ReleaseCodename
	}
	return ""
}

func (x *EndOfLifeRelease) GetReleaseCycle() string {
	if x != nil {
		return x.ReleaseCycle
	}
	return ""
}

func (x *EndOfLifeRelease) GetLatestVersion() string {
	if x != nil {
		return x.LatestVersion
	}
	return ""
}

func (x *EndOfLifeRelease) GetFirstReleaseDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FirstReleaseDate
	}
	return nil
}

func (x *EndOfLifeRelease) GetLastReleaseDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastReleaseDate
	}
	return nil
}

func (x *EndOfLifeRelease) GetReleaseLink() string {
	if x != nil {
		return x.ReleaseLink
	}
	return ""
}

func (x *EndOfLifeRelease) GetEndOfActiveSupport() *timestamppb.Timestamp {
	if x != nil {
		return x.EndOfActiveSupport
	}
	return nil
}

func (x *EndOfLifeRelease) GetEndOfLife() *timestamppb.Timestamp {
	if x != nil {
		return x.EndOfLife
	}
	return nil
}

func (x *EndOfLifeRelease) GetEndOfExtendedSupport() *timestamppb.Timestamp {
	if x != nil {
		return x.EndOfExtendedSupport
	}
	return nil
}

func (x *EndOfLifeRelease) GetChangeLogReference() string {
	if x != nil {
		return x.ChangeLogReference
	}
	return ""
}

var File_mvd_proto protoreflect.FileDescriptor

const file_mvd_proto_rawDesc = "" +
	"\n" +
	"\tmvd.proto\x12\rmondoo.mvd.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a-providers-sdk/v1/upstream/mvd/cvss/cvss.proto\"\xf0\x01\n" +
	"\bPlatform\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\arelease\x18\x02 \x01(\tR\arelease\x12\x12\n" +
	"\x04arch\x18\x03 \x01(\tR\x04arch\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x14\n" +
	"\x05build\x18\x06 \x01(\tR\x05build\x12;\n" +
	"\x06labels\x18\x16 \x03(\v2#.mondoo.mvd.v1.Platform.LabelsEntryR\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb3\x02\n" +
	"\aPackage\x12\x0e\n" +
	"\x02ID\x18\x06 \x01(\tR\x02ID\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x12\n" +
	"\x04arch\x18\x05 \x01(\tR\x04arch\x12 \n" +
	"\vdescription\x18\x14 \x01(\tR\vdescription\x12\x16\n" +
	"\x06format\x18\x17 \x01(\tR\x06format\x12\x16\n" +
	"\x06origin\x18\x18 \x01(\tR\x06origin\x12\x1c\n" +
	"\tavailable\x18\x16 \x01(\tR\tavailable\x12\x16\n" +
	"\x06status\x18\x15 \x01(\tR\x06status\x12\x1c\n" +
	"\tnamespace\x18\x19 \x01(\tR\tnamespace\x12\x14\n" +
	"\x05score\x18\x1a \x01(\x05R\x05score\x12\x1a\n" +
	"\baffected\x18\x1b \x01(\bR\baffected\"\xa4\x01\n" +
	"\x13AnalyseAssetRequest\x123\n" +
	"\bplatform\x18\x01 \x01(\v2\x17.mondoo.mvd.v1.PlatformR\bplatform\x122\n" +
	"\bpackages\x18\x02 \x03(\v2\x16.mondoo.mvd.v1.PackageR\bpackages\x12$\n" +
	"\rkernelVersion\x18\x03 \x01(\tR\rkernelVersion\"\xfe\x01\n" +
	"\n" +
	"VulnReport\x123\n" +
	"\bplatform\x18\x01 \x01(\v2\x17.mondoo.mvd.v1.PlatformR\bplatform\x122\n" +
	"\bpackages\x18\x02 \x03(\v2\x16.mondoo.mvd.v1.PackageR\bpackages\x127\n" +
	"\n" +
	"advisories\x18\x03 \x03(\v2\x17.mondoo.mvd.v1.AdvisoryR\n" +
	"advisories\x120\n" +
	"\x05stats\x18\x04 \x01(\v2\x1a.mondoo.mvd.v1.ReportStatsR\x05stats\x12\x1c\n" +
	"\tpublished\x18\x05 \x01(\tR\tpublished\"\x88\x05\n" +
	"\bAdvisory\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x10\n" +
	"\x03Mrn\x18\x02 \x01(\tR\x03Mrn\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12,\n" +
	"\x05fixed\x18\x05 \x03(\v2\x16.mondoo.mvd.v1.PackageR\x05fixed\x122\n" +
	"\baffected\x18\x06 \x03(\v2\x16.mondoo.mvd.v1.PackageR\baffected\x12,\n" +
	"\x04refs\x18\a \x03(\v2\x18.mondoo.mvd.v1.ReferenceR\x04refs\x12&\n" +
	"\x04cves\x18\x14 \x03(\v2\x12.mondoo.mvd.v1.CVER\x04cves\x12D\n" +
	"\x0efixedPlatforms\x18\x1e \x03(\v2\x1c.mondoo.mvd.v1.FixedPlatformR\x0efixedPlatforms\x12\x14\n" +
	"\x05score\x18\x19 \x01(\x05R\x05score\x128\n" +
	"\n" +
	"worstScore\x18\x1f \x01(\v2\x18.mondoo.mvd.cvss.v1.CvssR\n" +
	"worstScore\x12 \n" +
	"\vvendorscore\x18\x1a \x01(\x02R\vvendorscore\x122\n" +
	"\x05state\x18\x1b \x01(\x0e2\x1c.mondoo.mvd.v1.AdvisoryStateR\x05state\x12\x1c\n" +
	"\tpublished\x18\x17 \x01(\tR\tpublished\x12\x1a\n" +
	"\bmodified\x18\x18 \x01(\tR\bmodified\x12\"\n" +
	"\fsupersedence\x18\x1c \x03(\tR\fsupersedence\x12\x1a\n" +
	"\bunscored\x18\x1d \x01(\bR\bunscoredJ\x04\b\x15\x10\x16\"[\n" +
	"\tReference\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\x12\x16\n" +
	"\x06source\x18\x03 \x01(\tR\x06source\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\"\xb3\x01\n" +
	"\rFixedPlatform\x12\x0e\n" +
	"\x02ID\x18\x06 \x01(\tR\x02ID\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\arelease\x18\x02 \x01(\tR\arelease\x12\x12\n" +
	"\x04arch\x18\x03 \x01(\tR\x04arch\x12\x1c\n" +
	"\tnamespace\x18\x04 \x01(\tR\tnamespace\x12\x14\n" +
	"\x05build\x18\x05 \x01(\tR\x05build\x12\x1c\n" +
	"\tpatchName\x18\a \x01(\tR\tpatchName\"\xd5\x02\n" +
	"\vReportStats\x12\x14\n" +
	"\x05score\x18\x01 \x01(\x05R\x05score\x12\x1a\n" +
	"\baffected\x18\x02 \x01(\bR\baffected\x12\x1a\n" +
	"\bunscored\x18\x03 \x01(\bR\bunscored\x12D\n" +
	"\n" +
	"advisories\x18\x10 \x01(\v2$.mondoo.mvd.v1.ReportStatsAdvisoriesR\n" +
	"advisories\x122\n" +
	"\x04cves\x18\x11 \x01(\v2\x1e.mondoo.mvd.v1.ReportStatsCvesR\x04cves\x12>\n" +
	"\bpackages\x18\x12 \x01(\v2\".mondoo.mvd.v1.ReportStatsPackagesR\bpackages\x12>\n" +
	"\bexploits\x18\x13 \x01(\v2\".mondoo.mvd.v1.ReportStatsExploitsR\bexploits\"\xb5\x01\n" +
	"\x15ReportStatsAdvisories\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\x12\x1a\n" +
	"\bcritical\x18\x02 \x01(\x05R\bcritical\x12\x12\n" +
	"\x04high\x18\x03 \x01(\x05R\x04high\x12\x16\n" +
	"\x06medium\x18\x04 \x01(\x05R\x06medium\x12\x10\n" +
	"\x03low\x18\x05 \x01(\x05R\x03low\x12\x12\n" +
	"\x04none\x18\x06 \x01(\x05R\x04none\x12\x18\n" +
	"\aunknown\x18\a \x01(\x05R\aunknown\"\xaf\x01\n" +
	"\x0fReportStatsCves\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\x12\x1a\n" +
	"\bcritical\x18\x02 \x01(\x05R\bcritical\x12\x12\n" +
	"\x04high\x18\x03 \x01(\x05R\x04high\x12\x16\n" +
	"\x06medium\x18\x04 \x01(\x05R\x06medium\x12\x10\n" +
	"\x03low\x18\x05 \x01(\x05R\x03low\x12\x12\n" +
	"\x04none\x18\x06 \x01(\x05R\x04none\x12\x18\n" +
	"\aunknown\x18\a \x01(\x05R\aunknown\"\xcf\x01\n" +
	"\x13ReportStatsPackages\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\x12\x1a\n" +
	"\baffected\x18\x02 \x01(\x05R\baffected\x12\x1a\n" +
	"\bcritical\x18\x03 \x01(\x05R\bcritical\x12\x12\n" +
	"\x04high\x18\x04 \x01(\x05R\x04high\x12\x16\n" +
	"\x06medium\x18\x05 \x01(\x05R\x06medium\x12\x10\n" +
	"\x03low\x18\x06 \x01(\x05R\x03low\x12\x12\n" +
	"\x04none\x18\a \x01(\x05R\x04none\x12\x18\n" +
	"\aunknown\x18\b \x01(\x05R\aunknown\"+\n" +
	"\x13ReportStatsExploits\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x05R\x05total\"\xe8\x02\n" +
	"\x03CVE\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x10\n" +
	"\x03Mrn\x18\x02 \x01(\tR\x03Mrn\x12\x18\n" +
	"\asummary\x18\x03 \x01(\tR\asummary\x12\x14\n" +
	"\x05score\x18\x04 \x01(\x02R\x05score\x128\n" +
	"\n" +
	"worstScore\x18\a \x01(\v2\x18.mondoo.mvd.cvss.v1.CvssR\n" +
	"worstScore\x12\x1a\n" +
	"\bunscored\x18\x06 \x01(\bR\bunscored\x12-\n" +
	"\x05state\x18\x05 \x01(\x0e2\x17.mondoo.mvd.v1.CveStateR\x05state\x12,\n" +
	"\x04cvss\x18\x14 \x03(\v2\x18.mondoo.mvd.cvss.v1.CvssR\x04cvss\x12\x10\n" +
	"\x03cwe\x18\x15 \x01(\tR\x03cwe\x12\x1c\n" +
	"\tpublished\x18\x16 \x01(\tR\tpublished\x12\x1a\n" +
	"\bmodified\x18\x17 \x01(\tR\bmodified\x12\x10\n" +
	"\x03url\x18\x18 \x01(\tR\x03url\"\xbc\x01\n" +
	"\x0fPlatformEolInfo\x123\n" +
	"\bplatform\x18\x01 \x01(\v2\x17.mondoo.mvd.v1.PlatformR\bplatform\x12\x18\n" +
	"\aDocsUrl\x18\x02 \x01(\tR\aDocsUrl\x12\x1e\n" +
	"\n" +
	"ProductUrl\x18\x03 \x01(\tR\n" +
	"ProductUrl\x12 \n" +
	"\vReleaseDate\x18\x04 \x01(\tR\vReleaseDate\x12\x18\n" +
	"\aEolDate\x18\x05 \x01(\tR\aEolDate\"D\n" +
	"\x14GetProductEolRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\"R\n" +
	"\x15GetProductEolResponse\x129\n" +
	"\arelease\x18\x01 \x01(\v2\x1f.mondoo.mvd.v1.EndOfLifeReleaseR\arelease\"\xf1\x04\n" +
	"\x10EndOfLifeRelease\x12!\n" +
	"\frelease_name\x18\x01 \x01(\tR\vreleaseName\x12)\n" +
	"\x10release_codename\x18\x02 \x01(\tR\x0freleaseCodename\x12#\n" +
	"\rrelease_cycle\x18\x03 \x01(\tR\freleaseCycle\x12%\n" +
	"\x0elatest_version\x18\x04 \x01(\tR\rlatestVersion\x12H\n" +
	"\x12first_release_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x10firstReleaseDate\x12F\n" +
	"\x11last_release_date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x0flastReleaseDate\x12!\n" +
	"\frelease_link\x18\a \x01(\tR\vreleaseLink\x12M\n" +
	"\x15end_of_active_support\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\x12endOfActiveSupport\x12:\n" +
	"\vend_of_life\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tendOfLife\x12Q\n" +
	"\x17end_of_extended_support\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\x14endOfExtendedSupport\x120\n" +
	"\x14change_log_reference\x18\v \x01(\tR\x12changeLogReference*I\n" +
	"\rAdvisoryState\x12\f\n" +
	"\bRELEASED\x10\x00\x12\v\n" +
	"\aPENDING\x10\x01\x12\v\n" +
	"\aIGNORED\x10\x02\x12\x10\n" +
	"\fNOT_AFFECTED\x10\x03*o\n" +
	"\bCveState\x12\n" +
	"\n" +
	"\x06PUBLIC\x10\x00\x12\v\n" +
	"\aINVALID\x10\x01\x12\f\n" +
	"\bRESERVED\x10\x02\x12\x0f\n" +
	"\vREPLACED_BY\x10\x04\x12\x0e\n" +
	"\n" +
	"SPLIT_FROM\x10\x05\x12\r\n" +
	"\tMERGED_TO\x10\x06\x12\f\n" +
	"\bREJECTED\x10\a2\xfe\x01\n" +
	"\x0fAdvisoryScanner\x12M\n" +
	"\fAnalyseAsset\x12\".mondoo.mvd.v1.AnalyseAssetRequest\x1a\x19.mondoo.mvd.v1.VulnReport\x12@\n" +
	"\x05IsEol\x12\x17.mondoo.mvd.v1.Platform\x1a\x1e.mondoo.mvd.v1.PlatformEolInfo\x12Z\n" +
	"\rGetProductEol\x12#.mondoo.mvd.v1.GetProductEolRequest\x1a$.mondoo.mvd.v1.GetProductEolResponseB9Z7go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream/mvdb\x06proto3"

var (
	file_mvd_proto_rawDescOnce sync.Once
	file_mvd_proto_rawDescData []byte
)

func file_mvd_proto_rawDescGZIP() []byte {
	file_mvd_proto_rawDescOnce.Do(func() {
		file_mvd_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_mvd_proto_rawDesc), len(file_mvd_proto_rawDesc)))
	})
	return file_mvd_proto_rawDescData
}

var file_mvd_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_mvd_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_mvd_proto_goTypes = []any{
	(AdvisoryState)(0),            // 0: mondoo.mvd.v1.AdvisoryState
	(CveState)(0),                 // 1: mondoo.mvd.v1.CveState
	(*Platform)(nil),              // 2: mondoo.mvd.v1.Platform
	(*Package)(nil),               // 3: mondoo.mvd.v1.Package
	(*AnalyseAssetRequest)(nil),   // 4: mondoo.mvd.v1.AnalyseAssetRequest
	(*VulnReport)(nil),            // 5: mondoo.mvd.v1.VulnReport
	(*Advisory)(nil),              // 6: mondoo.mvd.v1.Advisory
	(*Reference)(nil),             // 7: mondoo.mvd.v1.Reference
	(*FixedPlatform)(nil),         // 8: mondoo.mvd.v1.FixedPlatform
	(*ReportStats)(nil),           // 9: mondoo.mvd.v1.ReportStats
	(*ReportStatsAdvisories)(nil), // 10: mondoo.mvd.v1.ReportStatsAdvisories
	(*ReportStatsCves)(nil),       // 11: mondoo.mvd.v1.ReportStatsCves
	(*ReportStatsPackages)(nil),   // 12: mondoo.mvd.v1.ReportStatsPackages
	(*ReportStatsExploits)(nil),   // 13: mondoo.mvd.v1.ReportStatsExploits
	(*CVE)(nil),                   // 14: mondoo.mvd.v1.CVE
	(*PlatformEolInfo)(nil),       // 15: mondoo.mvd.v1.PlatformEolInfo
	(*GetProductEolRequest)(nil),  // 16: mondoo.mvd.v1.GetProductEolRequest
	(*GetProductEolResponse)(nil), // 17: mondoo.mvd.v1.GetProductEolResponse
	(*EndOfLifeRelease)(nil),      // 18: mondoo.mvd.v1.EndOfLifeRelease
	nil,                           // 19: mondoo.mvd.v1.Platform.LabelsEntry
	(*cvss.Cvss)(nil),             // 20: mondoo.mvd.cvss.v1.Cvss
	(*timestamppb.Timestamp)(nil), // 21: google.protobuf.Timestamp
}
var file_mvd_proto_depIdxs = []int32{
	19, // 0: mondoo.mvd.v1.Platform.labels:type_name -> mondoo.mvd.v1.Platform.LabelsEntry
	2,  // 1: mondoo.mvd.v1.AnalyseAssetRequest.platform:type_name -> mondoo.mvd.v1.Platform
	3,  // 2: mondoo.mvd.v1.AnalyseAssetRequest.packages:type_name -> mondoo.mvd.v1.Package
	2,  // 3: mondoo.mvd.v1.VulnReport.platform:type_name -> mondoo.mvd.v1.Platform
	3,  // 4: mondoo.mvd.v1.VulnReport.packages:type_name -> mondoo.mvd.v1.Package
	6,  // 5: mondoo.mvd.v1.VulnReport.advisories:type_name -> mondoo.mvd.v1.Advisory
	9,  // 6: mondoo.mvd.v1.VulnReport.stats:type_name -> mondoo.mvd.v1.ReportStats
	3,  // 7: mondoo.mvd.v1.Advisory.fixed:type_name -> mondoo.mvd.v1.Package
	3,  // 8: mondoo.mvd.v1.Advisory.affected:type_name -> mondoo.mvd.v1.Package
	7,  // 9: mondoo.mvd.v1.Advisory.refs:type_name -> mondoo.mvd.v1.Reference
	14, // 10: mondoo.mvd.v1.Advisory.cves:type_name -> mondoo.mvd.v1.CVE
	8,  // 11: mondoo.mvd.v1.Advisory.fixedPlatforms:type_name -> mondoo.mvd.v1.FixedPlatform
	20, // 12: mondoo.mvd.v1.Advisory.worstScore:type_name -> mondoo.mvd.cvss.v1.Cvss
	0,  // 13: mondoo.mvd.v1.Advisory.state:type_name -> mondoo.mvd.v1.AdvisoryState
	10, // 14: mondoo.mvd.v1.ReportStats.advisories:type_name -> mondoo.mvd.v1.ReportStatsAdvisories
	11, // 15: mondoo.mvd.v1.ReportStats.cves:type_name -> mondoo.mvd.v1.ReportStatsCves
	12, // 16: mondoo.mvd.v1.ReportStats.packages:type_name -> mondoo.mvd.v1.ReportStatsPackages
	13, // 17: mondoo.mvd.v1.ReportStats.exploits:type_name -> mondoo.mvd.v1.ReportStatsExploits
	20, // 18: mondoo.mvd.v1.CVE.worstScore:type_name -> mondoo.mvd.cvss.v1.Cvss
	1,  // 19: mondoo.mvd.v1.CVE.state:type_name -> mondoo.mvd.v1.CveState
	20, // 20: mondoo.mvd.v1.CVE.cvss:type_name -> mondoo.mvd.cvss.v1.Cvss
	2,  // 21: mondoo.mvd.v1.PlatformEolInfo.platform:type_name -> mondoo.mvd.v1.Platform
	18, // 22: mondoo.mvd.v1.GetProductEolResponse.release:type_name -> mondoo.mvd.v1.EndOfLifeRelease
	21, // 23: mondoo.mvd.v1.EndOfLifeRelease.first_release_date:type_name -> google.protobuf.Timestamp
	21, // 24: mondoo.mvd.v1.EndOfLifeRelease.last_release_date:type_name -> google.protobuf.Timestamp
	21, // 25: mondoo.mvd.v1.EndOfLifeRelease.end_of_active_support:type_name -> google.protobuf.Timestamp
	21, // 26: mondoo.mvd.v1.EndOfLifeRelease.end_of_life:type_name -> google.protobuf.Timestamp
	21, // 27: mondoo.mvd.v1.EndOfLifeRelease.end_of_extended_support:type_name -> google.protobuf.Timestamp
	4,  // 28: mondoo.mvd.v1.AdvisoryScanner.AnalyseAsset:input_type -> mondoo.mvd.v1.AnalyseAssetRequest
	2,  // 29: mondoo.mvd.v1.AdvisoryScanner.IsEol:input_type -> mondoo.mvd.v1.Platform
	16, // 30: mondoo.mvd.v1.AdvisoryScanner.GetProductEol:input_type -> mondoo.mvd.v1.GetProductEolRequest
	5,  // 31: mondoo.mvd.v1.AdvisoryScanner.AnalyseAsset:output_type -> mondoo.mvd.v1.VulnReport
	15, // 32: mondoo.mvd.v1.AdvisoryScanner.IsEol:output_type -> mondoo.mvd.v1.PlatformEolInfo
	17, // 33: mondoo.mvd.v1.AdvisoryScanner.GetProductEol:output_type -> mondoo.mvd.v1.GetProductEolResponse
	31, // [31:34] is the sub-list for method output_type
	28, // [28:31] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_mvd_proto_init() }
func file_mvd_proto_init() {
	if File_mvd_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_mvd_proto_rawDesc), len(file_mvd_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_mvd_proto_goTypes,
		DependencyIndexes: file_mvd_proto_depIdxs,
		EnumInfos:         file_mvd_proto_enumTypes,
		MessageInfos:      file_mvd_proto_msgTypes,
	}.Build()
	File_mvd_proto = out.File
	file_mvd_proto_goTypes = nil
	file_mvd_proto_depIdxs = nil
}
