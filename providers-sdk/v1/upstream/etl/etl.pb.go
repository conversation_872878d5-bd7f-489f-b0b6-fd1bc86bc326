// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.1
// source: etl.proto

package etl

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// VulnerabilityExchange are used for sharing information about vulnerabilities
// across systems. Cnquery uses this format as a main mean to store detected
// vulnerability information. The same document is generated independently from
// the source.
type VulnerabilityExchange struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. CVE or Advisory ID
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// Required. Summary of the vulnerability
	Summary string `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
	// Required. Source of the vulnerability
	Source        *Source `protobuf:"bytes,7,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VulnerabilityExchange) Reset() {
	*x = VulnerabilityExchange{}
	mi := &file_etl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VulnerabilityExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnerabilityExchange) ProtoMessage() {}

func (x *VulnerabilityExchange) ProtoReflect() protoreflect.Message {
	mi := &file_etl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnerabilityExchange.ProtoReflect.Descriptor instead.
func (*VulnerabilityExchange) Descriptor() ([]byte, []int) {
	return file_etl_proto_rawDescGZIP(), []int{0}
}

func (x *VulnerabilityExchange) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VulnerabilityExchange) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *VulnerabilityExchange) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

// Source is used to identify the source of the vulnerability.
type Source struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required. Name of the source
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. URL of the source
	Url           string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Source) Reset() {
	*x = Source{}
	mi := &file_etl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_etl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_etl_proto_rawDescGZIP(), []int{1}
}

func (x *Source) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Source) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_etl_proto protoreflect.FileDescriptor

const file_etl_proto_rawDesc = "" +
	"\n" +
	"\tetl.proto\x12\x0ecnquery.etl.v1\"q\n" +
	"\x15VulnerabilityExchange\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x18\n" +
	"\asummary\x18\x04 \x01(\tR\asummary\x12.\n" +
	"\x06source\x18\a \x01(\v2\x16.cnquery.etl.v1.SourceR\x06source\".\n" +
	"\x06Source\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03urlB9Z7go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream/etlb\x06proto3"

var (
	file_etl_proto_rawDescOnce sync.Once
	file_etl_proto_rawDescData []byte
)

func file_etl_proto_rawDescGZIP() []byte {
	file_etl_proto_rawDescOnce.Do(func() {
		file_etl_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_etl_proto_rawDesc), len(file_etl_proto_rawDesc)))
	})
	return file_etl_proto_rawDescData
}

var file_etl_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_etl_proto_goTypes = []any{
	(*VulnerabilityExchange)(nil), // 0: cnquery.etl.v1.VulnerabilityExchange
	(*Source)(nil),                // 1: cnquery.etl.v1.Source
}
var file_etl_proto_depIdxs = []int32{
	1, // 0: cnquery.etl.v1.VulnerabilityExchange.source:type_name -> cnquery.etl.v1.Source
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_etl_proto_init() }
func file_etl_proto_init() {
	if File_etl_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_etl_proto_rawDesc), len(file_etl_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_etl_proto_goTypes,
		DependencyIndexes: file_etl_proto_depIdxs,
		MessageInfos:      file_etl_proto_msgTypes,
	}.Build()
	File_etl_proto = out.File
	file_etl_proto_goTypes = nil
	file_etl_proto_depIdxs = nil
}
