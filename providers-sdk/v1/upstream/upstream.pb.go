// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: upstream.proto

package upstream

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpstreamConfig struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	AssetMrn      string                     `protobuf:"bytes,1,opt,name=asset_mrn,json=assetMrn,proto3" json:"asset_mrn,omitempty"`
	SpaceMrn      string                     `protobuf:"bytes,2,opt,name=space_mrn,json=spaceMrn,proto3" json:"space_mrn,omitempty"`
	ApiEndpoint   string                     `protobuf:"bytes,3,opt,name=api_endpoint,json=apiEndpoint,proto3" json:"api_endpoint,omitempty"`
	Incognito     bool                       `protobuf:"varint,4,opt,name=incognito,proto3" json:"incognito,omitempty"`
	Creds         *ServiceAccountCredentials `protobuf:"bytes,5,opt,name=creds,proto3" json:"creds,omitempty"`
	ApiProxy      string                     `protobuf:"bytes,6,opt,name=api_proxy,json=apiProxy,proto3" json:"api_proxy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpstreamConfig) Reset() {
	*x = UpstreamConfig{}
	mi := &file_upstream_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpstreamConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpstreamConfig) ProtoMessage() {}

func (x *UpstreamConfig) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpstreamConfig.ProtoReflect.Descriptor instead.
func (*UpstreamConfig) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{0}
}

func (x *UpstreamConfig) GetAssetMrn() string {
	if x != nil {
		return x.AssetMrn
	}
	return ""
}

func (x *UpstreamConfig) GetSpaceMrn() string {
	if x != nil {
		return x.SpaceMrn
	}
	return ""
}

func (x *UpstreamConfig) GetApiEndpoint() string {
	if x != nil {
		return x.ApiEndpoint
	}
	return ""
}

func (x *UpstreamConfig) GetIncognito() bool {
	if x != nil {
		return x.Incognito
	}
	return false
}

func (x *UpstreamConfig) GetCreds() *ServiceAccountCredentials {
	if x != nil {
		return x.Creds
	}
	return nil
}

func (x *UpstreamConfig) GetApiProxy() string {
	if x != nil {
		return x.ApiProxy
	}
	return ""
}

// ServiceAccountCredentials are used to authenticate with Mondoo Platform
type ServiceAccountCredentials struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MRN of the service account
	Mrn string `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	// Deprecated: use scope_mrn instead
	ParentMrn string `protobuf:"bytes,2,opt,name=parent_mrn,json=parentMrn,proto3" json:"parent_mrn,omitempty"`
	// PEM-encoded private key
	PrivateKey string `protobuf:"bytes,3,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	// PEM-encoded certificate
	Certificate string `protobuf:"bytes,4,opt,name=certificate,proto3" json:"certificate,omitempty"`
	// API Endpoint for the service account
	ApiEndpoint string `protobuf:"bytes,5,opt,name=api_endpoint,json=apiEndpoint,proto3" json:"api_endpoint,omitempty"`
	// Scope MRN for the service account, either organization or a space
	ScopeMrn      string `protobuf:"bytes,6,opt,name=scope_mrn,json=scopeMrn,proto3" json:"scope_mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAccountCredentials) Reset() {
	*x = ServiceAccountCredentials{}
	mi := &file_upstream_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAccountCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAccountCredentials) ProtoMessage() {}

func (x *ServiceAccountCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAccountCredentials.ProtoReflect.Descriptor instead.
func (*ServiceAccountCredentials) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceAccountCredentials) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *ServiceAccountCredentials) GetParentMrn() string {
	if x != nil {
		return x.ParentMrn
	}
	return ""
}

func (x *ServiceAccountCredentials) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *ServiceAccountCredentials) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *ServiceAccountCredentials) GetApiEndpoint() string {
	if x != nil {
		return x.ApiEndpoint
	}
	return ""
}

func (x *ServiceAccountCredentials) GetScopeMrn() string {
	if x != nil {
		return x.ScopeMrn
	}
	return ""
}

type Ping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Ping) Reset() {
	*x = Ping{}
	mi := &file_upstream_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Ping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ping) ProtoMessage() {}

func (x *Ping) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ping.ProtoReflect.Descriptor instead.
func (*Ping) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{2}
}

type Pong struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pong) Reset() {
	*x = Pong{}
	mi := &file_upstream_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pong) ProtoMessage() {}

func (x *Pong) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pong.ProtoReflect.Descriptor instead.
func (*Pong) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{3}
}

type AgentCheckinResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentCheckinResponse) Reset() {
	*x = AgentCheckinResponse{}
	mi := &file_upstream_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentCheckinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentCheckinResponse) ProtoMessage() {}

func (x *AgentCheckinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentCheckinResponse.ProtoReflect.Descriptor instead.
func (*AgentCheckinResponse) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{4}
}

type AgentRegistrationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	AgentInfo     *AgentInfo             `protobuf:"bytes,3,opt,name=agent_info,json=agentInfo,proto3" json:"agent_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentRegistrationRequest) Reset() {
	*x = AgentRegistrationRequest{}
	mi := &file_upstream_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentRegistrationRequest) ProtoMessage() {}

func (x *AgentRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentRegistrationRequest.ProtoReflect.Descriptor instead.
func (*AgentRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{5}
}

func (x *AgentRegistrationRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *AgentRegistrationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentRegistrationRequest) GetAgentInfo() *AgentInfo {
	if x != nil {
		return x.AgentInfo
	}
	return nil
}

type AgentInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Mrn              string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	Version          string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Build            string                 `protobuf:"bytes,3,opt,name=build,proto3" json:"build,omitempty"`
	PlatformName     string                 `protobuf:"bytes,4,opt,name=platform_name,json=platformName,proto3" json:"platform_name,omitempty"`
	PlatformRelease  string                 `protobuf:"bytes,5,opt,name=platform_release,json=platformRelease,proto3" json:"platform_release,omitempty"`
	PlatformArch     string                 `protobuf:"bytes,6,opt,name=platform_arch,json=platformArch,proto3" json:"platform_arch,omitempty"`
	PlatformIp       string                 `protobuf:"bytes,7,opt,name=platform_ip,json=platformIp,proto3" json:"platform_ip,omitempty"`
	PlatformHostname string                 `protobuf:"bytes,8,opt,name=platform_hostname,json=platformHostname,proto3" json:"platform_hostname,omitempty"`
	Labels           map[string]string      `protobuf:"bytes,18,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	PlatformId       string                 `protobuf:"bytes,20,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AgentInfo) Reset() {
	*x = AgentInfo{}
	mi := &file_upstream_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentInfo) ProtoMessage() {}

func (x *AgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentInfo.ProtoReflect.Descriptor instead.
func (*AgentInfo) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{6}
}

func (x *AgentInfo) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *AgentInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AgentInfo) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

func (x *AgentInfo) GetPlatformName() string {
	if x != nil {
		return x.PlatformName
	}
	return ""
}

func (x *AgentInfo) GetPlatformRelease() string {
	if x != nil {
		return x.PlatformRelease
	}
	return ""
}

func (x *AgentInfo) GetPlatformArch() string {
	if x != nil {
		return x.PlatformArch
	}
	return ""
}

func (x *AgentInfo) GetPlatformIp() string {
	if x != nil {
		return x.PlatformIp
	}
	return ""
}

func (x *AgentInfo) GetPlatformHostname() string {
	if x != nil {
		return x.PlatformHostname
	}
	return ""
}

func (x *AgentInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AgentInfo) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

type AgentRegistrationConfirmation struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	AgentMrn      string                     `protobuf:"bytes,1,opt,name=agent_mrn,json=agentMrn,proto3" json:"agent_mrn,omitempty"`
	Credential    *ServiceAccountCredentials `protobuf:"bytes,2,opt,name=credential,proto3" json:"credential,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentRegistrationConfirmation) Reset() {
	*x = AgentRegistrationConfirmation{}
	mi := &file_upstream_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentRegistrationConfirmation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentRegistrationConfirmation) ProtoMessage() {}

func (x *AgentRegistrationConfirmation) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentRegistrationConfirmation.ProtoReflect.Descriptor instead.
func (*AgentRegistrationConfirmation) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{7}
}

func (x *AgentRegistrationConfirmation) GetAgentMrn() string {
	if x != nil {
		return x.AgentMrn
	}
	return ""
}

func (x *AgentRegistrationConfirmation) GetCredential() *ServiceAccountCredentials {
	if x != nil {
		return x.Credential
	}
	return nil
}

type Mrn struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mrn) Reset() {
	*x = Mrn{}
	mi := &file_upstream_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mrn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mrn) ProtoMessage() {}

func (x *Mrn) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mrn.ProtoReflect.Descriptor instead.
func (*Mrn) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{8}
}

func (x *Mrn) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

type Confirmation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Confirmation) Reset() {
	*x = Confirmation{}
	mi := &file_upstream_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Confirmation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Confirmation) ProtoMessage() {}

func (x *Confirmation) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Confirmation.ProtoReflect.Descriptor instead.
func (*Confirmation) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{9}
}

func (x *Confirmation) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

type ExchangeSSHKeyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Claims        *Claims                `protobuf:"bytes,1,opt,name=claims,proto3" json:"claims,omitempty"`
	Signatures    []*SshSignature        `protobuf:"bytes,2,rep,name=signatures,proto3" json:"signatures,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeSSHKeyRequest) Reset() {
	*x = ExchangeSSHKeyRequest{}
	mi := &file_upstream_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeSSHKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeSSHKeyRequest) ProtoMessage() {}

func (x *ExchangeSSHKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeSSHKeyRequest.ProtoReflect.Descriptor instead.
func (*ExchangeSSHKeyRequest) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{10}
}

func (x *ExchangeSSHKeyRequest) GetClaims() *Claims {
	if x != nil {
		return x.Claims
	}
	return nil
}

func (x *ExchangeSSHKeyRequest) GetSignatures() []*SshSignature {
	if x != nil {
		return x.Signatures
	}
	return nil
}

type ExchangeSSHKeyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mrn           string                 `protobuf:"bytes,1,opt,name=mrn,proto3" json:"mrn,omitempty"`
	ParentMrn     string                 `protobuf:"bytes,2,opt,name=parent_mrn,json=parentMrn,proto3" json:"parent_mrn,omitempty"`
	PrivateKey    string                 `protobuf:"bytes,3,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	Certificate   string                 `protobuf:"bytes,4,opt,name=certificate,proto3" json:"certificate,omitempty"`
	ApiEndpoint   string                 `protobuf:"bytes,5,opt,name=api_endpoint,json=apiEndpoint,proto3" json:"api_endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeSSHKeyResponse) Reset() {
	*x = ExchangeSSHKeyResponse{}
	mi := &file_upstream_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeSSHKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeSSHKeyResponse) ProtoMessage() {}

func (x *ExchangeSSHKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeSSHKeyResponse.ProtoReflect.Descriptor instead.
func (*ExchangeSSHKeyResponse) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{11}
}

func (x *ExchangeSSHKeyResponse) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *ExchangeSSHKeyResponse) GetParentMrn() string {
	if x != nil {
		return x.ParentMrn
	}
	return ""
}

func (x *ExchangeSSHKeyResponse) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *ExchangeSSHKeyResponse) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *ExchangeSSHKeyResponse) GetApiEndpoint() string {
	if x != nil {
		return x.ApiEndpoint
	}
	return ""
}

// Claims follow https://datatracker.ietf.org/doc/html/rfc8693#section-2.1
type Claims struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Subject string                 `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	// NumericDate as defined in RFC 7519, which uses RFC 3339
	Exp string `protobuf:"bytes,2,opt,name=exp,proto3" json:"exp,omitempty"`
	// NumericDate as defined in RFC 7519, which uses RFC 3339
	Iat           string `protobuf:"bytes,3,opt,name=iat,proto3" json:"iat,omitempty"`
	Resource      string `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Claims) Reset() {
	*x = Claims{}
	mi := &file_upstream_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Claims) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Claims) ProtoMessage() {}

func (x *Claims) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Claims.ProtoReflect.Descriptor instead.
func (*Claims) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{12}
}

func (x *Claims) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Claims) GetExp() string {
	if x != nil {
		return x.Exp
	}
	return ""
}

func (x *Claims) GetIat() string {
	if x != nil {
		return x.Iat
	}
	return ""
}

func (x *Claims) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

type SshSignature struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Alg           string                 `protobuf:"bytes,1,opt,name=alg,proto3" json:"alg,omitempty"`
	Kid           string                 `protobuf:"bytes,2,opt,name=kid,proto3" json:"kid,omitempty"`
	Sig           string                 `protobuf:"bytes,3,opt,name=sig,proto3" json:"sig,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SshSignature) Reset() {
	*x = SshSignature{}
	mi := &file_upstream_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SshSignature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SshSignature) ProtoMessage() {}

func (x *SshSignature) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SshSignature.ProtoReflect.Descriptor instead.
func (*SshSignature) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{13}
}

func (x *SshSignature) GetAlg() string {
	if x != nil {
		return x.Alg
	}
	return ""
}

func (x *SshSignature) GetKid() string {
	if x != nil {
		return x.Kid
	}
	return ""
}

func (x *SshSignature) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

type ExchangeExternalTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Issuer URI of the external identity provider
	IssuerUri string `protobuf:"bytes,1,opt,name=issuer_uri,json=issuerUri,proto3" json:"issuer_uri,omitempty"`
	// Audience for the service account
	Audience string `protobuf:"bytes,2,opt,name=audience,proto3" json:"audience,omitempty"`
	// Token provided by the external identity provider to exchange
	JwtToken      string `protobuf:"bytes,3,opt,name=jwt_token,json=jwtToken,proto3" json:"jwt_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExchangeExternalTokenRequest) Reset() {
	*x = ExchangeExternalTokenRequest{}
	mi := &file_upstream_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeExternalTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeExternalTokenRequest) ProtoMessage() {}

func (x *ExchangeExternalTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeExternalTokenRequest.ProtoReflect.Descriptor instead.
func (*ExchangeExternalTokenRequest) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{14}
}

func (x *ExchangeExternalTokenRequest) GetIssuerUri() string {
	if x != nil {
		return x.IssuerUri
	}
	return ""
}

func (x *ExchangeExternalTokenRequest) GetAudience() string {
	if x != nil {
		return x.Audience
	}
	return ""
}

func (x *ExchangeExternalTokenRequest) GetJwtToken() string {
	if x != nil {
		return x.JwtToken
	}
	return ""
}

type ExchangeExternalTokenResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Base64 encoded service account credential
	Base64Credential string `protobuf:"bytes,1,opt,name=base64_credential,json=base64Credential,proto3" json:"base64_credential,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ExchangeExternalTokenResponse) Reset() {
	*x = ExchangeExternalTokenResponse{}
	mi := &file_upstream_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExchangeExternalTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeExternalTokenResponse) ProtoMessage() {}

func (x *ExchangeExternalTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_upstream_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeExternalTokenResponse.ProtoReflect.Descriptor instead.
func (*ExchangeExternalTokenResponse) Descriptor() ([]byte, []int) {
	return file_upstream_proto_rawDescGZIP(), []int{15}
}

func (x *ExchangeExternalTokenResponse) GetBase64Credential() string {
	if x != nil {
		return x.Base64Credential
	}
	return ""
}

var File_upstream_proto protoreflect.FileDescriptor

const file_upstream_proto_rawDesc = "" +
	"\n" +
	"\x0eupstream.proto\x12\x1amondoo.cnquery.upstream.v1\"\xf5\x01\n" +
	"\x0eUpstreamConfig\x12\x1b\n" +
	"\tasset_mrn\x18\x01 \x01(\tR\bassetMrn\x12\x1b\n" +
	"\tspace_mrn\x18\x02 \x01(\tR\bspaceMrn\x12!\n" +
	"\fapi_endpoint\x18\x03 \x01(\tR\vapiEndpoint\x12\x1c\n" +
	"\tincognito\x18\x04 \x01(\bR\tincognito\x12K\n" +
	"\x05creds\x18\x05 \x01(\v25.mondoo.cnquery.upstream.v1.ServiceAccountCredentialsR\x05creds\x12\x1b\n" +
	"\tapi_proxy\x18\x06 \x01(\tR\bapiProxy\"\xcf\x01\n" +
	"\x19ServiceAccountCredentials\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x1d\n" +
	"\n" +
	"parent_mrn\x18\x02 \x01(\tR\tparentMrn\x12\x1f\n" +
	"\vprivate_key\x18\x03 \x01(\tR\n" +
	"privateKey\x12 \n" +
	"\vcertificate\x18\x04 \x01(\tR\vcertificate\x12!\n" +
	"\fapi_endpoint\x18\x05 \x01(\tR\vapiEndpoint\x12\x1b\n" +
	"\tscope_mrn\x18\x06 \x01(\tR\bscopeMrn\"\x06\n" +
	"\x04Ping\"\x06\n" +
	"\x04Pong\"\x16\n" +
	"\x14AgentCheckinResponse\"\x8a\x01\n" +
	"\x18AgentRegistrationRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12D\n" +
	"\n" +
	"agent_info\x18\x03 \x01(\v2%.mondoo.cnquery.upstream.v1.AgentInfoR\tagentInfo\"\xb7\x03\n" +
	"\tAgentInfo\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x14\n" +
	"\x05build\x18\x03 \x01(\tR\x05build\x12#\n" +
	"\rplatform_name\x18\x04 \x01(\tR\fplatformName\x12)\n" +
	"\x10platform_release\x18\x05 \x01(\tR\x0fplatformRelease\x12#\n" +
	"\rplatform_arch\x18\x06 \x01(\tR\fplatformArch\x12\x1f\n" +
	"\vplatform_ip\x18\a \x01(\tR\n" +
	"platformIp\x12+\n" +
	"\x11platform_hostname\x18\b \x01(\tR\x10platformHostname\x12I\n" +
	"\x06labels\x18\x12 \x03(\v21.mondoo.cnquery.upstream.v1.AgentInfo.LabelsEntryR\x06labels\x12\x1f\n" +
	"\vplatform_id\x18\x14 \x01(\tR\n" +
	"platformId\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x93\x01\n" +
	"\x1dAgentRegistrationConfirmation\x12\x1b\n" +
	"\tagent_mrn\x18\x01 \x01(\tR\bagentMrn\x12U\n" +
	"\n" +
	"credential\x18\x02 \x01(\v25.mondoo.cnquery.upstream.v1.ServiceAccountCredentialsR\n" +
	"credential\"\x17\n" +
	"\x03Mrn\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\" \n" +
	"\fConfirmation\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\"\x9d\x01\n" +
	"\x15ExchangeSSHKeyRequest\x12:\n" +
	"\x06claims\x18\x01 \x01(\v2\".mondoo.cnquery.upstream.v1.ClaimsR\x06claims\x12H\n" +
	"\n" +
	"signatures\x18\x02 \x03(\v2(.mondoo.cnquery.upstream.v1.SshSignatureR\n" +
	"signatures\"\xaf\x01\n" +
	"\x16ExchangeSSHKeyResponse\x12\x10\n" +
	"\x03mrn\x18\x01 \x01(\tR\x03mrn\x12\x1d\n" +
	"\n" +
	"parent_mrn\x18\x02 \x01(\tR\tparentMrn\x12\x1f\n" +
	"\vprivate_key\x18\x03 \x01(\tR\n" +
	"privateKey\x12 \n" +
	"\vcertificate\x18\x04 \x01(\tR\vcertificate\x12!\n" +
	"\fapi_endpoint\x18\x05 \x01(\tR\vapiEndpoint\"b\n" +
	"\x06Claims\x12\x18\n" +
	"\asubject\x18\x01 \x01(\tR\asubject\x12\x10\n" +
	"\x03exp\x18\x02 \x01(\tR\x03exp\x12\x10\n" +
	"\x03iat\x18\x03 \x01(\tR\x03iat\x12\x1a\n" +
	"\bresource\x18\x04 \x01(\tR\bresource\"D\n" +
	"\fSshSignature\x12\x10\n" +
	"\x03alg\x18\x01 \x01(\tR\x03alg\x12\x10\n" +
	"\x03kid\x18\x02 \x01(\tR\x03kid\x12\x10\n" +
	"\x03sig\x18\x03 \x01(\tR\x03sig\"v\n" +
	"\x1cExchangeExternalTokenRequest\x12\x1d\n" +
	"\n" +
	"issuer_uri\x18\x01 \x01(\tR\tissuerUri\x12\x1a\n" +
	"\baudience\x18\x02 \x01(\tR\baudience\x12\x1b\n" +
	"\tjwt_token\x18\x03 \x01(\tR\bjwtToken\"L\n" +
	"\x1dExchangeExternalTokenResponse\x12+\n" +
	"\x11base64_credential\x18\x01 \x01(\tR\x10base64Credential2\xa7\x03\n" +
	"\fAgentManager\x12\x80\x01\n" +
	"\rRegisterAgent\x124.mondoo.cnquery.upstream.v1.AgentRegistrationRequest\x1a9.mondoo.cnquery.upstream.v1.AgentRegistrationConfirmation\x12\\\n" +
	"\x0fUnRegisterAgent\x12\x1f.mondoo.cnquery.upstream.v1.Mrn\x1a(.mondoo.cnquery.upstream.v1.Confirmation\x12N\n" +
	"\bPingPong\x12 .mondoo.cnquery.upstream.v1.Ping\x1a .mondoo.cnquery.upstream.v1.Pong\x12f\n" +
	"\vHealthCheck\x12%.mondoo.cnquery.upstream.v1.AgentInfo\x1a0.mondoo.cnquery.upstream.v1.AgentCheckinResponse2\x9d\x02\n" +
	"\x12SecureTokenService\x12v\n" +
	"\vExchangeSSH\x121.mondoo.cnquery.upstream.v1.ExchangeSSHKeyRequest\x1a2.mondoo.cnquery.upstream.v1.ExchangeSSHKeyResponse\"\x00\x12\x8e\x01\n" +
	"\x15ExchangeExternalToken\x128.mondoo.cnquery.upstream.v1.ExchangeExternalTokenRequest\x1a9.mondoo.cnquery.upstream.v1.ExchangeExternalTokenResponse\"\x00B5Z3go.mondoo.com/cnquery/v11/providers-sdk/v1/upstreamb\x06proto3"

var (
	file_upstream_proto_rawDescOnce sync.Once
	file_upstream_proto_rawDescData []byte
)

func file_upstream_proto_rawDescGZIP() []byte {
	file_upstream_proto_rawDescOnce.Do(func() {
		file_upstream_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_upstream_proto_rawDesc), len(file_upstream_proto_rawDesc)))
	})
	return file_upstream_proto_rawDescData
}

var file_upstream_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_upstream_proto_goTypes = []any{
	(*UpstreamConfig)(nil),                // 0: mondoo.cnquery.upstream.v1.UpstreamConfig
	(*ServiceAccountCredentials)(nil),     // 1: mondoo.cnquery.upstream.v1.ServiceAccountCredentials
	(*Ping)(nil),                          // 2: mondoo.cnquery.upstream.v1.Ping
	(*Pong)(nil),                          // 3: mondoo.cnquery.upstream.v1.Pong
	(*AgentCheckinResponse)(nil),          // 4: mondoo.cnquery.upstream.v1.AgentCheckinResponse
	(*AgentRegistrationRequest)(nil),      // 5: mondoo.cnquery.upstream.v1.AgentRegistrationRequest
	(*AgentInfo)(nil),                     // 6: mondoo.cnquery.upstream.v1.AgentInfo
	(*AgentRegistrationConfirmation)(nil), // 7: mondoo.cnquery.upstream.v1.AgentRegistrationConfirmation
	(*Mrn)(nil),                           // 8: mondoo.cnquery.upstream.v1.Mrn
	(*Confirmation)(nil),                  // 9: mondoo.cnquery.upstream.v1.Confirmation
	(*ExchangeSSHKeyRequest)(nil),         // 10: mondoo.cnquery.upstream.v1.ExchangeSSHKeyRequest
	(*ExchangeSSHKeyResponse)(nil),        // 11: mondoo.cnquery.upstream.v1.ExchangeSSHKeyResponse
	(*Claims)(nil),                        // 12: mondoo.cnquery.upstream.v1.Claims
	(*SshSignature)(nil),                  // 13: mondoo.cnquery.upstream.v1.SshSignature
	(*ExchangeExternalTokenRequest)(nil),  // 14: mondoo.cnquery.upstream.v1.ExchangeExternalTokenRequest
	(*ExchangeExternalTokenResponse)(nil), // 15: mondoo.cnquery.upstream.v1.ExchangeExternalTokenResponse
	nil,                                   // 16: mondoo.cnquery.upstream.v1.AgentInfo.LabelsEntry
}
var file_upstream_proto_depIdxs = []int32{
	1,  // 0: mondoo.cnquery.upstream.v1.UpstreamConfig.creds:type_name -> mondoo.cnquery.upstream.v1.ServiceAccountCredentials
	6,  // 1: mondoo.cnquery.upstream.v1.AgentRegistrationRequest.agent_info:type_name -> mondoo.cnquery.upstream.v1.AgentInfo
	16, // 2: mondoo.cnquery.upstream.v1.AgentInfo.labels:type_name -> mondoo.cnquery.upstream.v1.AgentInfo.LabelsEntry
	1,  // 3: mondoo.cnquery.upstream.v1.AgentRegistrationConfirmation.credential:type_name -> mondoo.cnquery.upstream.v1.ServiceAccountCredentials
	12, // 4: mondoo.cnquery.upstream.v1.ExchangeSSHKeyRequest.claims:type_name -> mondoo.cnquery.upstream.v1.Claims
	13, // 5: mondoo.cnquery.upstream.v1.ExchangeSSHKeyRequest.signatures:type_name -> mondoo.cnquery.upstream.v1.SshSignature
	5,  // 6: mondoo.cnquery.upstream.v1.AgentManager.RegisterAgent:input_type -> mondoo.cnquery.upstream.v1.AgentRegistrationRequest
	8,  // 7: mondoo.cnquery.upstream.v1.AgentManager.UnRegisterAgent:input_type -> mondoo.cnquery.upstream.v1.Mrn
	2,  // 8: mondoo.cnquery.upstream.v1.AgentManager.PingPong:input_type -> mondoo.cnquery.upstream.v1.Ping
	6,  // 9: mondoo.cnquery.upstream.v1.AgentManager.HealthCheck:input_type -> mondoo.cnquery.upstream.v1.AgentInfo
	10, // 10: mondoo.cnquery.upstream.v1.SecureTokenService.ExchangeSSH:input_type -> mondoo.cnquery.upstream.v1.ExchangeSSHKeyRequest
	14, // 11: mondoo.cnquery.upstream.v1.SecureTokenService.ExchangeExternalToken:input_type -> mondoo.cnquery.upstream.v1.ExchangeExternalTokenRequest
	7,  // 12: mondoo.cnquery.upstream.v1.AgentManager.RegisterAgent:output_type -> mondoo.cnquery.upstream.v1.AgentRegistrationConfirmation
	9,  // 13: mondoo.cnquery.upstream.v1.AgentManager.UnRegisterAgent:output_type -> mondoo.cnquery.upstream.v1.Confirmation
	3,  // 14: mondoo.cnquery.upstream.v1.AgentManager.PingPong:output_type -> mondoo.cnquery.upstream.v1.Pong
	4,  // 15: mondoo.cnquery.upstream.v1.AgentManager.HealthCheck:output_type -> mondoo.cnquery.upstream.v1.AgentCheckinResponse
	11, // 16: mondoo.cnquery.upstream.v1.SecureTokenService.ExchangeSSH:output_type -> mondoo.cnquery.upstream.v1.ExchangeSSHKeyResponse
	15, // 17: mondoo.cnquery.upstream.v1.SecureTokenService.ExchangeExternalToken:output_type -> mondoo.cnquery.upstream.v1.ExchangeExternalTokenResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_upstream_proto_init() }
func file_upstream_proto_init() {
	if File_upstream_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_upstream_proto_rawDesc), len(file_upstream_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_upstream_proto_goTypes,
		DependencyIndexes: file_upstream_proto_depIdxs,
		MessageInfos:      file_upstream_proto_msgTypes,
	}.Build()
	File_upstream_proto = out.File
	file_upstream_proto_goTypes = nil
	file_upstream_proto_depIdxs = nil
}
