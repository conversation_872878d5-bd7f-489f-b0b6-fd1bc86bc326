// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: errors.proto

package health

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendErrorReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ServiceAccountMrn string                 `protobuf:"bytes,1,opt,name=service_account_mrn,json=serviceAccountMrn,proto3" json:"service_account_mrn,omitempty"`
	AgentMrn          string                 `protobuf:"bytes,2,opt,name=agent_mrn,json=agentMrn,proto3" json:"agent_mrn,omitempty"`
	Product           *ProductInfo           `protobuf:"bytes,3,opt,name=product,proto3" json:"product,omitempty"`
	Error             *ErrorInfo             `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SendErrorReq) Reset() {
	*x = SendErrorReq{}
	mi := &file_errors_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendErrorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendErrorReq) ProtoMessage() {}

func (x *SendErrorReq) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendErrorReq.ProtoReflect.Descriptor instead.
func (*SendErrorReq) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{0}
}

func (x *SendErrorReq) GetServiceAccountMrn() string {
	if x != nil {
		return x.ServiceAccountMrn
	}
	return ""
}

func (x *SendErrorReq) GetAgentMrn() string {
	if x != nil {
		return x.AgentMrn
	}
	return ""
}

func (x *SendErrorReq) GetProduct() *ProductInfo {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *SendErrorReq) GetError() *ErrorInfo {
	if x != nil {
		return x.Error
	}
	return nil
}

type ProductInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Build         string                 `protobuf:"bytes,3,opt,name=build,proto3" json:"build,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProductInfo) Reset() {
	*x = ProductInfo{}
	mi := &file_errors_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProductInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductInfo) ProtoMessage() {}

func (x *ProductInfo) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductInfo.ProtoReflect.Descriptor instead.
func (*ProductInfo) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{1}
}

func (x *ProductInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProductInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ProductInfo) GetBuild() string {
	if x != nil {
		return x.Build
	}
	return ""
}

type ErrorInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Stacktrace    string                 `protobuf:"bytes,2,opt,name=stacktrace,proto3" json:"stacktrace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorInfo) Reset() {
	*x = ErrorInfo{}
	mi := &file_errors_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorInfo) ProtoMessage() {}

func (x *ErrorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorInfo.ProtoReflect.Descriptor instead.
func (*ErrorInfo) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{2}
}

func (x *ErrorInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorInfo) GetStacktrace() string {
	if x != nil {
		return x.Stacktrace
	}
	return ""
}

type SendErrorResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendErrorResp) Reset() {
	*x = SendErrorResp{}
	mi := &file_errors_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendErrorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendErrorResp) ProtoMessage() {}

func (x *SendErrorResp) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendErrorResp.ProtoReflect.Descriptor instead.
func (*SendErrorResp) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{3}
}

var File_errors_proto protoreflect.FileDescriptor

const file_errors_proto_rawDesc = "" +
	"\n" +
	"\ferrors.proto\x12\x13go.mondoo.health.v1\"\xcd\x01\n" +
	"\fSendErrorReq\x12.\n" +
	"\x13service_account_mrn\x18\x01 \x01(\tR\x11serviceAccountMrn\x12\x1b\n" +
	"\tagent_mrn\x18\x02 \x01(\tR\bagentMrn\x12:\n" +
	"\aproduct\x18\x03 \x01(\v2 .go.mondoo.health.v1.ProductInfoR\aproduct\x124\n" +
	"\x05error\x18\x04 \x01(\v2\x1e.go.mondoo.health.v1.ErrorInfoR\x05error\"Q\n" +
	"\vProductInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x14\n" +
	"\x05build\x18\x03 \x01(\tR\x05build\"E\n" +
	"\tErrorInfo\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"stacktrace\x18\x02 \x01(\tR\n" +
	"stacktrace\"\x0f\n" +
	"\rSendErrorResp2d\n" +
	"\x0eErrorReporting\x12R\n" +
	"\tSendError\x12!.go.mondoo.health.v1.SendErrorReq\x1a\".go.mondoo.health.v1.SendErrorRespB\x1dZ\x1bgo.mondoo.com/mondoo/healthb\x06proto3"

var (
	file_errors_proto_rawDescOnce sync.Once
	file_errors_proto_rawDescData []byte
)

func file_errors_proto_rawDescGZIP() []byte {
	file_errors_proto_rawDescOnce.Do(func() {
		file_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errors_proto_rawDesc), len(file_errors_proto_rawDesc)))
	})
	return file_errors_proto_rawDescData
}

var file_errors_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_errors_proto_goTypes = []any{
	(*SendErrorReq)(nil),  // 0: go.mondoo.health.v1.SendErrorReq
	(*ProductInfo)(nil),   // 1: go.mondoo.health.v1.ProductInfo
	(*ErrorInfo)(nil),     // 2: go.mondoo.health.v1.ErrorInfo
	(*SendErrorResp)(nil), // 3: go.mondoo.health.v1.SendErrorResp
}
var file_errors_proto_depIdxs = []int32{
	1, // 0: go.mondoo.health.v1.SendErrorReq.product:type_name -> go.mondoo.health.v1.ProductInfo
	2, // 1: go.mondoo.health.v1.SendErrorReq.error:type_name -> go.mondoo.health.v1.ErrorInfo
	0, // 2: go.mondoo.health.v1.ErrorReporting.SendError:input_type -> go.mondoo.health.v1.SendErrorReq
	3, // 3: go.mondoo.health.v1.ErrorReporting.SendError:output_type -> go.mondoo.health.v1.SendErrorResp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_errors_proto_init() }
func file_errors_proto_init() {
	if File_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errors_proto_rawDesc), len(file_errors_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_errors_proto_goTypes,
		DependencyIndexes: file_errors_proto_depIdxs,
		MessageInfos:      file_errors_proto_msgTypes,
	}.Build()
	File_errors_proto = out.File
	file_errors_proto_goTypes = nil
	file_errors_proto_depIdxs = nil
}
