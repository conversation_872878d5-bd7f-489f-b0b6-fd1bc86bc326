// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: plugin.proto

package plugin

import (
	llx "go.mondoo.com/cnquery/v11/llx"
	inventory "go.mondoo.com/cnquery/v11/providers-sdk/v1/inventory"
	upstream "go.mondoo.com/cnquery/v11/providers-sdk/v1/upstream"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ParseCLIReq struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Connector     string                    `protobuf:"bytes,1,opt,name=connector,proto3" json:"connector,omitempty"`
	Args          []string                  `protobuf:"bytes,2,rep,name=args,proto3" json:"args,omitempty"`
	Flags         map[string]*llx.Primitive `protobuf:"bytes,3,rep,name=flags,proto3" json:"flags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseCLIReq) Reset() {
	*x = ParseCLIReq{}
	mi := &file_plugin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseCLIReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseCLIReq) ProtoMessage() {}

func (x *ParseCLIReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseCLIReq.ProtoReflect.Descriptor instead.
func (*ParseCLIReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{0}
}

func (x *ParseCLIReq) GetConnector() string {
	if x != nil {
		return x.Connector
	}
	return ""
}

func (x *ParseCLIReq) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ParseCLIReq) GetFlags() map[string]*llx.Primitive {
	if x != nil {
		return x.Flags
	}
	return nil
}

type ParseCLIRes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// inventory after parsing of CLI; no connection, no discovery, no resolution
	Asset         *inventory.Asset `protobuf:"bytes,1,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseCLIRes) Reset() {
	*x = ParseCLIRes{}
	mi := &file_plugin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseCLIRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseCLIRes) ProtoMessage() {}

func (x *ParseCLIRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseCLIRes.ProtoReflect.Descriptor instead.
func (*ParseCLIRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{1}
}

func (x *ParseCLIRes) GetAsset() *inventory.Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type ConnectReq struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Features []byte                 `protobuf:"bytes,2,opt,name=features,proto3" json:"features,omitempty"`
	// The one primary targeted asset for the connection
	Asset          *inventory.Asset         `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	HasRecording   bool                     `protobuf:"varint,20,opt,name=has_recording,json=hasRecording,proto3" json:"has_recording,omitempty"`
	CallbackServer uint32                   `protobuf:"varint,21,opt,name=callback_server,json=callbackServer,proto3" json:"callback_server,omitempty"`
	Upstream       *upstream.UpstreamConfig `protobuf:"bytes,22,opt,name=upstream,proto3" json:"upstream,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConnectReq) Reset() {
	*x = ConnectReq{}
	mi := &file_plugin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectReq) ProtoMessage() {}

func (x *ConnectReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectReq.ProtoReflect.Descriptor instead.
func (*ConnectReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{2}
}

func (x *ConnectReq) GetFeatures() []byte {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *ConnectReq) GetAsset() *inventory.Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *ConnectReq) GetHasRecording() bool {
	if x != nil {
		return x.HasRecording
	}
	return false
}

func (x *ConnectReq) GetCallbackServer() uint32 {
	if x != nil {
		return x.CallbackServer
	}
	return 0
}

func (x *ConnectReq) GetUpstream() *upstream.UpstreamConfig {
	if x != nil {
		return x.Upstream
	}
	return nil
}

type ConnectRes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// the connected asset with additional information from this connection
	Asset *inventory.Asset `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	// inventory of other discovered assets
	Inventory     *inventory.Inventory `protobuf:"bytes,4,opt,name=inventory,proto3" json:"inventory,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectRes) Reset() {
	*x = ConnectRes{}
	mi := &file_plugin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectRes) ProtoMessage() {}

func (x *ConnectRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectRes.ProtoReflect.Descriptor instead.
func (*ConnectRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{3}
}

func (x *ConnectRes) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConnectRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConnectRes) GetAsset() *inventory.Asset {
	if x != nil {
		return x.Asset
	}
	return nil
}

func (x *ConnectRes) GetInventory() *inventory.Inventory {
	if x != nil {
		return x.Inventory
	}
	return nil
}

type ShutdownReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShutdownReq) Reset() {
	*x = ShutdownReq{}
	mi := &file_plugin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShutdownReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShutdownReq) ProtoMessage() {}

func (x *ShutdownReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShutdownReq.ProtoReflect.Descriptor instead.
func (*ShutdownReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{4}
}

type ShutdownRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShutdownRes) Reset() {
	*x = ShutdownRes{}
	mi := &file_plugin_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShutdownRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShutdownRes) ProtoMessage() {}

func (x *ShutdownRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShutdownRes.ProtoReflect.Descriptor instead.
func (*ShutdownRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{5}
}

type DataReq struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Connection    uint32                    `protobuf:"varint,1,opt,name=connection,proto3" json:"connection,omitempty"`
	Resource      string                    `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
	ResourceId    string                    `protobuf:"bytes,4,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	Field         string                    `protobuf:"bytes,5,opt,name=field,proto3" json:"field,omitempty"`
	Args          map[string]*llx.Primitive `protobuf:"bytes,6,rep,name=args,proto3" json:"args,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataReq) Reset() {
	*x = DataReq{}
	mi := &file_plugin_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReq) ProtoMessage() {}

func (x *DataReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReq.ProtoReflect.Descriptor instead.
func (*DataReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{6}
}

func (x *DataReq) GetConnection() uint32 {
	if x != nil {
		return x.Connection
	}
	return 0
}

func (x *DataReq) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *DataReq) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *DataReq) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *DataReq) GetArgs() map[string]*llx.Primitive {
	if x != nil {
		return x.Args
	}
	return nil
}

type DataRes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Data  *llx.Primitive         `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Error string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	// The ID uniquely identifies this request and all associated callbacks
	Id            string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataRes) Reset() {
	*x = DataRes{}
	mi := &file_plugin_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataRes) ProtoMessage() {}

func (x *DataRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataRes.ProtoReflect.Descriptor instead.
func (*DataRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{7}
}

func (x *DataRes) GetData() *llx.Primitive {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DataRes) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *DataRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CollectRes) Reset() {
	*x = CollectRes{}
	mi := &file_plugin_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectRes) ProtoMessage() {}

func (x *CollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectRes.ProtoReflect.Descriptor instead.
func (*CollectRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{8}
}

type StoreReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Connection    uint32                 `protobuf:"varint,1,opt,name=connection,proto3" json:"connection,omitempty"`
	Resources     []*ResourceData        `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoreReq) Reset() {
	*x = StoreReq{}
	mi := &file_plugin_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoreReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreReq) ProtoMessage() {}

func (x *StoreReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreReq.ProtoReflect.Descriptor instead.
func (*StoreReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{9}
}

func (x *StoreReq) GetConnection() uint32 {
	if x != nil {
		return x.Connection
	}
	return 0
}

func (x *StoreReq) GetResources() []*ResourceData {
	if x != nil {
		return x.Resources
	}
	return nil
}

type ResourceData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Id            string                 `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	Fields        map[string]*llx.Result `protobuf:"bytes,5,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceData) Reset() {
	*x = ResourceData{}
	mi := &file_plugin_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceData) ProtoMessage() {}

func (x *ResourceData) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceData.ProtoReflect.Descriptor instead.
func (*ResourceData) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{10}
}

func (x *ResourceData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResourceData) GetFields() map[string]*llx.Result {
	if x != nil {
		return x.Fields
	}
	return nil
}

type StoreRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoreRes) Reset() {
	*x = StoreRes{}
	mi := &file_plugin_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoreRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreRes) ProtoMessage() {}

func (x *StoreRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreRes.ProtoReflect.Descriptor instead.
func (*StoreRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{11}
}

type HeartbeatReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// interval in nanoseconds until the next heartbeat occurs
	Interval      uint64 `protobuf:"varint,1,opt,name=interval,proto3" json:"interval,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatReq) Reset() {
	*x = HeartbeatReq{}
	mi := &file_plugin_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReq) ProtoMessage() {}

func (x *HeartbeatReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReq.ProtoReflect.Descriptor instead.
func (*HeartbeatReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{12}
}

func (x *HeartbeatReq) GetInterval() uint64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type HeartbeatRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatRes) Reset() {
	*x = HeartbeatRes{}
	mi := &file_plugin_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRes) ProtoMessage() {}

func (x *HeartbeatRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRes.ProtoReflect.Descriptor instead.
func (*HeartbeatRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{13}
}

type DisconnectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Connection    uint32                 `protobuf:"varint,1,opt,name=connection,proto3" json:"connection,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisconnectReq) Reset() {
	*x = DisconnectReq{}
	mi := &file_plugin_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisconnectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisconnectReq) ProtoMessage() {}

func (x *DisconnectReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisconnectReq.ProtoReflect.Descriptor instead.
func (*DisconnectReq) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{14}
}

func (x *DisconnectReq) GetConnection() uint32 {
	if x != nil {
		return x.Connection
	}
	return 0
}

type DisconnectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisconnectRes) Reset() {
	*x = DisconnectRes{}
	mi := &file_plugin_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisconnectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisconnectRes) ProtoMessage() {}

func (x *DisconnectRes) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisconnectRes.ProtoReflect.Descriptor instead.
func (*DisconnectRes) Descriptor() ([]byte, []int) {
	return file_plugin_proto_rawDescGZIP(), []int{15}
}

var File_plugin_proto protoreflect.FileDescriptor

const file_plugin_proto_rawDesc = "" +
	"\n" +
	"\fplugin.proto\x12\x14cnquery.providers.v1\x1a\rllx/llx.proto\x1a*providers-sdk/v1/inventory/inventory.proto\x1a(providers-sdk/v1/upstream/upstream.proto\"\xd5\x01\n" +
	"\vParseCLIReq\x12\x1c\n" +
	"\tconnector\x18\x01 \x01(\tR\tconnector\x12\x12\n" +
	"\x04args\x18\x02 \x03(\tR\x04args\x12B\n" +
	"\x05flags\x18\x03 \x03(\v2,.cnquery.providers.v1.ParseCLIReq.FlagsEntryR\x05flags\x1aP\n" +
	"\n" +
	"FlagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.cnquery.llx.PrimitiveR\x05value:\x028\x01\"@\n" +
	"\vParseCLIRes\x121\n" +
	"\x05asset\x18\x01 \x01(\v2\x1b.cnquery.providers.v1.AssetR\x05asset\"\xf1\x01\n" +
	"\n" +
	"ConnectReq\x12\x1a\n" +
	"\bfeatures\x18\x02 \x01(\fR\bfeatures\x121\n" +
	"\x05asset\x18\x03 \x01(\v2\x1b.cnquery.providers.v1.AssetR\x05asset\x12#\n" +
	"\rhas_recording\x18\x14 \x01(\bR\fhasRecording\x12'\n" +
	"\x0fcallback_server\x18\x15 \x01(\rR\x0ecallbackServer\x12F\n" +
	"\bupstream\x18\x16 \x01(\v2*.mondoo.cnquery.upstream.v1.UpstreamConfigR\bupstream\"\xa2\x01\n" +
	"\n" +
	"ConnectRes\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x121\n" +
	"\x05asset\x18\x03 \x01(\v2\x1b.cnquery.providers.v1.AssetR\x05asset\x12=\n" +
	"\tinventory\x18\x04 \x01(\v2\x1f.cnquery.providers.v1.InventoryR\tinventory\"\r\n" +
	"\vShutdownReq\"\r\n" +
	"\vShutdownRes\"\x8a\x02\n" +
	"\aDataReq\x12\x1e\n" +
	"\n" +
	"connection\x18\x01 \x01(\rR\n" +
	"connection\x12\x1a\n" +
	"\bresource\x18\x03 \x01(\tR\bresource\x12\x1f\n" +
	"\vresource_id\x18\x04 \x01(\tR\n" +
	"resourceId\x12\x14\n" +
	"\x05field\x18\x05 \x01(\tR\x05field\x12;\n" +
	"\x04args\x18\x06 \x03(\v2'.cnquery.providers.v1.DataReq.ArgsEntryR\x04args\x1aO\n" +
	"\tArgsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.cnquery.llx.PrimitiveR\x05value:\x028\x01\"[\n" +
	"\aDataRes\x12*\n" +
	"\x04data\x18\x01 \x01(\v2\x16.cnquery.llx.PrimitiveR\x04data\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\tR\x02id\"\f\n" +
	"\n" +
	"CollectRes\"l\n" +
	"\bStoreReq\x12\x1e\n" +
	"\n" +
	"connection\x18\x01 \x01(\rR\n" +
	"connection\x12@\n" +
	"\tresources\x18\x02 \x03(\v2\".cnquery.providers.v1.ResourceDataR\tresources\"\xca\x01\n" +
	"\fResourceData\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x0e\n" +
	"\x02id\x18\x04 \x01(\tR\x02id\x12F\n" +
	"\x06fields\x18\x05 \x03(\v2..cnquery.providers.v1.ResourceData.FieldsEntryR\x06fields\x1aN\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12)\n" +
	"\x05value\x18\x02 \x01(\v2\x13.cnquery.llx.ResultR\x05value:\x028\x01\"\n" +
	"\n" +
	"\bStoreRes\"*\n" +
	"\fHeartbeatReq\x12\x1a\n" +
	"\binterval\x18\x01 \x01(\x04R\binterval\"\x0e\n" +
	"\fHeartbeatRes\"/\n" +
	"\rDisconnectReq\x12\x1e\n" +
	"\n" +
	"connection\x18\x01 \x01(\rR\n" +
	"connection\"\x0f\n" +
	"\rDisconnectRes2\x99\x05\n" +
	"\x0eProviderPlugin\x12S\n" +
	"\tHeartbeat\x12\".cnquery.providers.v1.HeartbeatReq\x1a\".cnquery.providers.v1.HeartbeatRes\x12P\n" +
	"\bParseCLI\x12!.cnquery.providers.v1.ParseCLIReq\x1a!.cnquery.providers.v1.ParseCLIRes\x12M\n" +
	"\aConnect\x12 .cnquery.providers.v1.ConnectReq\x1a .cnquery.providers.v1.ConnectRes\x12V\n" +
	"\n" +
	"Disconnect\x12#.cnquery.providers.v1.DisconnectReq\x1a#.cnquery.providers.v1.DisconnectRes\x12Q\n" +
	"\vMockConnect\x12 .cnquery.providers.v1.ConnectReq\x1a .cnquery.providers.v1.ConnectRes\x12P\n" +
	"\bShutdown\x12!.cnquery.providers.v1.ShutdownReq\x1a!.cnquery.providers.v1.ShutdownRes\x12G\n" +
	"\aGetData\x12\x1d.cnquery.providers.v1.DataReq\x1a\x1d.cnquery.providers.v1.DataRes\x12K\n" +
	"\tStoreData\x12\x1e.cnquery.providers.v1.StoreReq\x1a\x1e.cnquery.providers.v1.StoreRes2\xfa\x01\n" +
	"\x10ProviderCallback\x12J\n" +
	"\aCollect\x12\x1d.cnquery.providers.v1.DataRes\x1a .cnquery.providers.v1.CollectRes\x12Q\n" +
	"\fGetRecording\x12\x1d.cnquery.providers.v1.DataReq\x1a\".cnquery.providers.v1.ResourceData\x12G\n" +
	"\aGetData\x12\x1d.cnquery.providers.v1.DataReq\x1a\x1d.cnquery.providers.v1.DataResB3Z1go.mondoo.com/cnquery/v11/providers-sdk/v1/pluginb\x06proto3"

var (
	file_plugin_proto_rawDescOnce sync.Once
	file_plugin_proto_rawDescData []byte
)

func file_plugin_proto_rawDescGZIP() []byte {
	file_plugin_proto_rawDescOnce.Do(func() {
		file_plugin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_plugin_proto_rawDesc), len(file_plugin_proto_rawDesc)))
	})
	return file_plugin_proto_rawDescData
}

var file_plugin_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_plugin_proto_goTypes = []any{
	(*ParseCLIReq)(nil),             // 0: cnquery.providers.v1.ParseCLIReq
	(*ParseCLIRes)(nil),             // 1: cnquery.providers.v1.ParseCLIRes
	(*ConnectReq)(nil),              // 2: cnquery.providers.v1.ConnectReq
	(*ConnectRes)(nil),              // 3: cnquery.providers.v1.ConnectRes
	(*ShutdownReq)(nil),             // 4: cnquery.providers.v1.ShutdownReq
	(*ShutdownRes)(nil),             // 5: cnquery.providers.v1.ShutdownRes
	(*DataReq)(nil),                 // 6: cnquery.providers.v1.DataReq
	(*DataRes)(nil),                 // 7: cnquery.providers.v1.DataRes
	(*CollectRes)(nil),              // 8: cnquery.providers.v1.CollectRes
	(*StoreReq)(nil),                // 9: cnquery.providers.v1.StoreReq
	(*ResourceData)(nil),            // 10: cnquery.providers.v1.ResourceData
	(*StoreRes)(nil),                // 11: cnquery.providers.v1.StoreRes
	(*HeartbeatReq)(nil),            // 12: cnquery.providers.v1.HeartbeatReq
	(*HeartbeatRes)(nil),            // 13: cnquery.providers.v1.HeartbeatRes
	(*DisconnectReq)(nil),           // 14: cnquery.providers.v1.DisconnectReq
	(*DisconnectRes)(nil),           // 15: cnquery.providers.v1.DisconnectRes
	nil,                             // 16: cnquery.providers.v1.ParseCLIReq.FlagsEntry
	nil,                             // 17: cnquery.providers.v1.DataReq.ArgsEntry
	nil,                             // 18: cnquery.providers.v1.ResourceData.FieldsEntry
	(*inventory.Asset)(nil),         // 19: cnquery.providers.v1.Asset
	(*upstream.UpstreamConfig)(nil), // 20: mondoo.cnquery.upstream.v1.UpstreamConfig
	(*inventory.Inventory)(nil),     // 21: cnquery.providers.v1.Inventory
	(*llx.Primitive)(nil),           // 22: cnquery.llx.Primitive
	(*llx.Result)(nil),              // 23: cnquery.llx.Result
}
var file_plugin_proto_depIdxs = []int32{
	16, // 0: cnquery.providers.v1.ParseCLIReq.flags:type_name -> cnquery.providers.v1.ParseCLIReq.FlagsEntry
	19, // 1: cnquery.providers.v1.ParseCLIRes.asset:type_name -> cnquery.providers.v1.Asset
	19, // 2: cnquery.providers.v1.ConnectReq.asset:type_name -> cnquery.providers.v1.Asset
	20, // 3: cnquery.providers.v1.ConnectReq.upstream:type_name -> mondoo.cnquery.upstream.v1.UpstreamConfig
	19, // 4: cnquery.providers.v1.ConnectRes.asset:type_name -> cnquery.providers.v1.Asset
	21, // 5: cnquery.providers.v1.ConnectRes.inventory:type_name -> cnquery.providers.v1.Inventory
	17, // 6: cnquery.providers.v1.DataReq.args:type_name -> cnquery.providers.v1.DataReq.ArgsEntry
	22, // 7: cnquery.providers.v1.DataRes.data:type_name -> cnquery.llx.Primitive
	10, // 8: cnquery.providers.v1.StoreReq.resources:type_name -> cnquery.providers.v1.ResourceData
	18, // 9: cnquery.providers.v1.ResourceData.fields:type_name -> cnquery.providers.v1.ResourceData.FieldsEntry
	22, // 10: cnquery.providers.v1.ParseCLIReq.FlagsEntry.value:type_name -> cnquery.llx.Primitive
	22, // 11: cnquery.providers.v1.DataReq.ArgsEntry.value:type_name -> cnquery.llx.Primitive
	23, // 12: cnquery.providers.v1.ResourceData.FieldsEntry.value:type_name -> cnquery.llx.Result
	12, // 13: cnquery.providers.v1.ProviderPlugin.Heartbeat:input_type -> cnquery.providers.v1.HeartbeatReq
	0,  // 14: cnquery.providers.v1.ProviderPlugin.ParseCLI:input_type -> cnquery.providers.v1.ParseCLIReq
	2,  // 15: cnquery.providers.v1.ProviderPlugin.Connect:input_type -> cnquery.providers.v1.ConnectReq
	14, // 16: cnquery.providers.v1.ProviderPlugin.Disconnect:input_type -> cnquery.providers.v1.DisconnectReq
	2,  // 17: cnquery.providers.v1.ProviderPlugin.MockConnect:input_type -> cnquery.providers.v1.ConnectReq
	4,  // 18: cnquery.providers.v1.ProviderPlugin.Shutdown:input_type -> cnquery.providers.v1.ShutdownReq
	6,  // 19: cnquery.providers.v1.ProviderPlugin.GetData:input_type -> cnquery.providers.v1.DataReq
	9,  // 20: cnquery.providers.v1.ProviderPlugin.StoreData:input_type -> cnquery.providers.v1.StoreReq
	7,  // 21: cnquery.providers.v1.ProviderCallback.Collect:input_type -> cnquery.providers.v1.DataRes
	6,  // 22: cnquery.providers.v1.ProviderCallback.GetRecording:input_type -> cnquery.providers.v1.DataReq
	6,  // 23: cnquery.providers.v1.ProviderCallback.GetData:input_type -> cnquery.providers.v1.DataReq
	13, // 24: cnquery.providers.v1.ProviderPlugin.Heartbeat:output_type -> cnquery.providers.v1.HeartbeatRes
	1,  // 25: cnquery.providers.v1.ProviderPlugin.ParseCLI:output_type -> cnquery.providers.v1.ParseCLIRes
	3,  // 26: cnquery.providers.v1.ProviderPlugin.Connect:output_type -> cnquery.providers.v1.ConnectRes
	15, // 27: cnquery.providers.v1.ProviderPlugin.Disconnect:output_type -> cnquery.providers.v1.DisconnectRes
	3,  // 28: cnquery.providers.v1.ProviderPlugin.MockConnect:output_type -> cnquery.providers.v1.ConnectRes
	5,  // 29: cnquery.providers.v1.ProviderPlugin.Shutdown:output_type -> cnquery.providers.v1.ShutdownRes
	7,  // 30: cnquery.providers.v1.ProviderPlugin.GetData:output_type -> cnquery.providers.v1.DataRes
	11, // 31: cnquery.providers.v1.ProviderPlugin.StoreData:output_type -> cnquery.providers.v1.StoreRes
	8,  // 32: cnquery.providers.v1.ProviderCallback.Collect:output_type -> cnquery.providers.v1.CollectRes
	10, // 33: cnquery.providers.v1.ProviderCallback.GetRecording:output_type -> cnquery.providers.v1.ResourceData
	7,  // 34: cnquery.providers.v1.ProviderCallback.GetData:output_type -> cnquery.providers.v1.DataRes
	24, // [24:35] is the sub-list for method output_type
	13, // [13:24] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_plugin_proto_init() }
func file_plugin_proto_init() {
	if File_plugin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_plugin_proto_rawDesc), len(file_plugin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_plugin_proto_goTypes,
		DependencyIndexes: file_plugin_proto_depIdxs,
		MessageInfos:      file_plugin_proto_msgTypes,
	}.Build()
	File_plugin_proto = out.File
	file_plugin_proto_goTypes = nil
	file_plugin_proto_depIdxs = nil
}
