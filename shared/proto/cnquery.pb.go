// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cnquery.proto

package proto

import (
	inventory "go.mondoo.com/cnquery/v11/providers-sdk/v1/inventory"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RunQueryConfig struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Command         string                 `protobuf:"bytes,1,opt,name=command,proto3" json:"command,omitempty"`
	CallbackServer  uint32                 `protobuf:"varint,2,opt,name=callback_server,json=callbackServer,proto3" json:"callback_server,omitempty"`
	Inventory       *inventory.Inventory   `protobuf:"bytes,3,opt,name=inventory,proto3" json:"inventory,omitempty"`
	Features        []byte                 `protobuf:"bytes,4,opt,name=features,proto3" json:"features,omitempty"`
	DoParse         bool                   `protobuf:"varint,5,opt,name=do_parse,json=doParse,proto3" json:"do_parse,omitempty"`
	DoAst           bool                   `protobuf:"varint,6,opt,name=do_ast,json=doAst,proto3" json:"do_ast,omitempty"`
	DoInfo          bool                   `protobuf:"varint,13,opt,name=do_info,json=doInfo,proto3" json:"do_info,omitempty"`
	DoRecord        bool                   `protobuf:"varint,7,opt,name=do_record,json=doRecord,proto3" json:"do_record,omitempty"`
	Format          string                 `protobuf:"bytes,8,opt,name=format,proto3" json:"format,omitempty"`
	PlatformId      string                 `protobuf:"bytes,9,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	Incognito       bool                   `protobuf:"varint,10,opt,name=incognito,proto3" json:"incognito,omitempty"`
	Output          string                 `protobuf:"bytes,11,opt,name=output,proto3" json:"output,omitempty"`
	Input           string                 `protobuf:"bytes,12,opt,name=input,proto3" json:"input,omitempty"`
	Exit_1OnFailure bool                   `protobuf:"varint,14,opt,name=exit_1_on_failure,json=exit1OnFailure,proto3" json:"exit_1_on_failure,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RunQueryConfig) Reset() {
	*x = RunQueryConfig{}
	mi := &file_cnquery_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunQueryConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunQueryConfig) ProtoMessage() {}

func (x *RunQueryConfig) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunQueryConfig.ProtoReflect.Descriptor instead.
func (*RunQueryConfig) Descriptor() ([]byte, []int) {
	return file_cnquery_proto_rawDescGZIP(), []int{0}
}

func (x *RunQueryConfig) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *RunQueryConfig) GetCallbackServer() uint32 {
	if x != nil {
		return x.CallbackServer
	}
	return 0
}

func (x *RunQueryConfig) GetInventory() *inventory.Inventory {
	if x != nil {
		return x.Inventory
	}
	return nil
}

func (x *RunQueryConfig) GetFeatures() []byte {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *RunQueryConfig) GetDoParse() bool {
	if x != nil {
		return x.DoParse
	}
	return false
}

func (x *RunQueryConfig) GetDoAst() bool {
	if x != nil {
		return x.DoAst
	}
	return false
}

func (x *RunQueryConfig) GetDoInfo() bool {
	if x != nil {
		return x.DoInfo
	}
	return false
}

func (x *RunQueryConfig) GetDoRecord() bool {
	if x != nil {
		return x.DoRecord
	}
	return false
}

func (x *RunQueryConfig) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *RunQueryConfig) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *RunQueryConfig) GetIncognito() bool {
	if x != nil {
		return x.Incognito
	}
	return false
}

func (x *RunQueryConfig) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *RunQueryConfig) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *RunQueryConfig) GetExit_1OnFailure() bool {
	if x != nil {
		return x.Exit_1OnFailure
	}
	return false
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_cnquery_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_cnquery_proto_rawDescGZIP(), []int{1}
}

type String struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *String) Reset() {
	*x = String{}
	mi := &file_cnquery_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *String) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*String) ProtoMessage() {}

func (x *String) ProtoReflect() protoreflect.Message {
	mi := &file_cnquery_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use String.ProtoReflect.Descriptor instead.
func (*String) Descriptor() ([]byte, []int) {
	return file_cnquery_proto_rawDescGZIP(), []int{2}
}

func (x *String) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_cnquery_proto protoreflect.FileDescriptor

const file_cnquery_proto_rawDesc = "" +
	"\n" +
	"\rcnquery.proto\x12\x05proto\x1a*providers-sdk/v1/inventory/inventory.proto\"\xc6\x03\n" +
	"\x0eRunQueryConfig\x12\x18\n" +
	"\acommand\x18\x01 \x01(\tR\acommand\x12'\n" +
	"\x0fcallback_server\x18\x02 \x01(\rR\x0ecallbackServer\x12=\n" +
	"\tinventory\x18\x03 \x01(\v2\x1f.cnquery.providers.v1.InventoryR\tinventory\x12\x1a\n" +
	"\bfeatures\x18\x04 \x01(\fR\bfeatures\x12\x19\n" +
	"\bdo_parse\x18\x05 \x01(\bR\adoParse\x12\x15\n" +
	"\x06do_ast\x18\x06 \x01(\bR\x05doAst\x12\x17\n" +
	"\ado_info\x18\r \x01(\bR\x06doInfo\x12\x1b\n" +
	"\tdo_record\x18\a \x01(\bR\bdoRecord\x12\x16\n" +
	"\x06format\x18\b \x01(\tR\x06format\x12\x1f\n" +
	"\vplatform_id\x18\t \x01(\tR\n" +
	"platformId\x12\x1c\n" +
	"\tincognito\x18\n" +
	" \x01(\bR\tincognito\x12\x16\n" +
	"\x06output\x18\v \x01(\tR\x06output\x12\x14\n" +
	"\x05input\x18\f \x01(\tR\x05input\x12)\n" +
	"\x11exit_1_on_failure\x18\x0e \x01(\bR\x0eexit1OnFailure\"\a\n" +
	"\x05Empty\"\x1c\n" +
	"\x06String\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data2:\n" +
	"\aCNQuery\x12/\n" +
	"\bRunQuery\x12\x15.proto.RunQueryConfig\x1a\f.proto.Empty24\n" +
	"\fOutputHelper\x12$\n" +
	"\x05Write\x12\r.proto.String\x1a\f.proto.EmptyB(Z&go.mondoo.com/cnquery/v11/shared/protob\x06proto3"

var (
	file_cnquery_proto_rawDescOnce sync.Once
	file_cnquery_proto_rawDescData []byte
)

func file_cnquery_proto_rawDescGZIP() []byte {
	file_cnquery_proto_rawDescOnce.Do(func() {
		file_cnquery_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cnquery_proto_rawDesc), len(file_cnquery_proto_rawDesc)))
	})
	return file_cnquery_proto_rawDescData
}

var file_cnquery_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_cnquery_proto_goTypes = []any{
	(*RunQueryConfig)(nil),      // 0: proto.RunQueryConfig
	(*Empty)(nil),               // 1: proto.Empty
	(*String)(nil),              // 2: proto.String
	(*inventory.Inventory)(nil), // 3: cnquery.providers.v1.Inventory
}
var file_cnquery_proto_depIdxs = []int32{
	3, // 0: proto.RunQueryConfig.inventory:type_name -> cnquery.providers.v1.Inventory
	0, // 1: proto.CNQuery.RunQuery:input_type -> proto.RunQueryConfig
	2, // 2: proto.OutputHelper.Write:input_type -> proto.String
	1, // 3: proto.CNQuery.RunQuery:output_type -> proto.Empty
	1, // 4: proto.OutputHelper.Write:output_type -> proto.Empty
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_cnquery_proto_init() }
func file_cnquery_proto_init() {
	if File_cnquery_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cnquery_proto_rawDesc), len(file_cnquery_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_cnquery_proto_goTypes,
		DependencyIndexes: file_cnquery_proto_depIdxs,
		MessageInfos:      file_cnquery_proto_msgTypes,
	}.Build()
	File_cnquery_proto = out.File
	file_cnquery_proto_goTypes = nil
	file_cnquery_proto_depIdxs = nil
}
